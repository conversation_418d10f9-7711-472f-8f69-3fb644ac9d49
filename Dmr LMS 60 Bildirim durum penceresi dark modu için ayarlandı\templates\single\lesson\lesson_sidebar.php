<?php
/**
 * Display Topics and Lesson lists for learn
 *
 * @package <PERSON><PERSON>\Templates
 * @subpackage Single\Lesson
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.0.0
 */

use TUTOR\Input;
use Tu<PERSON>\Models\QuizModel;
use TUTOR\Quiz;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// CSS stil değişiklikleri
?>
<style>
	.tutor-course-topic-item-icon {
		display: none !important;
	}
	.tutor-course-single-sidebar-wrapper .tutor-course-topic-item-icon {
		display: none !important;
	}
	.tutor-course-topic-item-thumbnail {
		width: 65px !important;
		height: 45px !important;
		border-radius: 4px !important;
		object-fit: cover !important;
		margin-right: 8px !important;
		vertical-align: middle !important;
	}
	.tutor-course-topic-item-gradient {
		width: 65px !important;
		height: 45px !important;
		border-radius: 4px !important;
		margin-right: 8px !important;
		background: var(--tutor-color-primary) !important;
		display: inline-block !important;
		position: relative !important;
	}
	/* Temel içerik ikonu stil ayarları */
	.tutor-course-topic-item-gradient::after {
		content: '';
		position: absolute !important;
		left: 50% !important;
		top: 50% !important;
		transform: translate(-50%, -50%) !important;
		width: 24px !important;
		height: 24px !important;
		background-size: contain !important;
		background-repeat: no-repeat !important;
		background-position: center !important;
	}

	/* Yazı metni ikonu (süre eklenmemiş içerikler için) */
	.tutor-course-topic-item-gradient::after {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.9)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E") !important;
	}

	/* Video ikonu (süre eklenmiş içerikler için) */
	.tutor-course-topic-item-has-video .tutor-course-topic-item-gradient::after {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.9)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolygon points='10 8 16 12 10 16 10 8'%3E%3C/polygon%3E%3C/svg%3E") !important;
	}

	/* Quiz için özel sınav ikonu - Soru İşareti */
	.tutor-course-topic-item-quiz .tutor-course-topic-item-gradient::after {
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,1)' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpath d='M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3'%3E%3C/path%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E") !important;
		background-size: 22px 22px !important;
	}
	.tutor-lesson-content-title-wrap {
		display: flex;
		flex-direction: column;
	}
	.tutor-course-topic-item-duration {
		margin-top: 2px !important;
		font-size: 12px !important;
	}
	.tutor-course-topic-item-title {
		max-width: 240px !important;
		text-overflow: ellipsis !important;
		overflow: hidden !important;
		white-space: nowrap !important;
	}

	/* Farklı ekran boyutlarına göre genişlik ayarları */
	/* 20px-360px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 20px) and (max-width: 360px) {
		.tutor-course-topic-item-title {
			max-width: 100px !important;
		}
	}

	/* 360px-500px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 360px) and (max-width: 500px) {
		.tutor-course-topic-item-title {
			max-width: 200px !important;
		}
	}

	/* 500px-768px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 500px) and (max-width: 768px) {
		.tutor-course-topic-item-title {
			max-width: 300px !important;
		}
	}

	/* 768px-1024px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 768px) and (max-width: 1024px) {
		.tutor-course-topic-item-title {
			max-width: 500px !important;
		}
	}

	/* 1024px-1200px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 1024px) and (max-width: 1200px) {
		.tutor-course-topic-item-title {
			max-width: 700px !important;
		}
	}

	/* Ders oluşturma sayfasında kapak fotoğrafı boş alanı için stil */
	#tutor-course-builder > div.css-34ma64 > div:nth-child(1) > div > div.css-19xi3wq > div > div.css-xejk3w > div:nth-child(1) > div > div.css-bjn8wh > div > div {
		background: linear-gradient(135deg, #1e5799 0%, #207cca 50%, #2989d8 100%) !important;
		border: none !important;
	}

	/* Ders başlığı için max-width ayarları */
	/* 20px-360px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 20px) and (max-width: 360px) {
		html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
			max-width: 50px !important;
		}
	}

	/* 360px-400px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 360px) and (max-width: 400px) {
		html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
			max-width: 150px !important;
		}
	}

	/* 400px-500px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 400px) and (max-width: 500px) {
		html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
			max-width: 180px !important;
		}
	}

	/* 500px-610px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 500px) and (max-width: 610px) {
		html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
			max-width: 200px !important;
		}
	}

	/* 610px-768px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 610px) and (max-width: 768px) {
		html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
			max-width: 350px !important;
		}
	}

	/* 768px-1024px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 768px) and (max-width: 1024px) {
		html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
			max-width: 500px !important;
		}
	}

	/* 1024px-1200px arası ekran boyutları için genişlik ayarı */
	@media (min-width: 1024px) and (max-width: 1200px) {
		html body .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header-title {
			max-width: 700px !important;
		}
	}

	/* Çarpı butonu hover efekti */
	.tutor-icon-times {
		transition: all 0.3s ease !important;
	}
	.tutor-iconic-btn:hover .tutor-icon-times {
		color: #e53935 !important;
		transform: scale(1.2) !important;
	}

	/* Çarpı butonu için border'ı kaldır */
	.tutor-iconic-btn {
		border: none !important;
	}
</style>
<?php

// Mavi gradient arka plan resmi için URL oluştur
// Base64 formatında encoded mavi gradient resmi (65x45px) - daha basit ve güvenilir format
$blue_gradient_bg = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NSIgaGVpZ2h0PSI0NSIgdmlld0JveD0iMCAwIDY1IDQ1Ij48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImEiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiMxODVBOUQiLz48c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iIzIwN2NjYSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzI5ODlkOCIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHdpZHRoPSI2NSIgaGVpZ2h0PSI0NSIgZmlsbD0idXJsKCNhKSIgcng9IjQiLz48L3N2Zz4=';

global $post;

$current_post_id = get_the_ID();
if ( ! empty( Input::post( 'lesson_id' ) ) ) {
	$current_post_id = Input::post( 'lesson_id' );
}

$current_post = $post;
$_is_preview  = get_post_meta( $current_post_id, '_is_preview', true );
$course_id    = tutor_utils()->get_course_id_by_subcontent( $post->ID );

$user_id                      = get_current_user_id();
$enable_qa_for_this_course    = get_post_meta( $course_id, '_tutor_enable_qa', true ) == 'yes';
$enable_q_and_a_on_course     = tutor_utils()->get_option( 'enable_q_and_a_on_course' ) && $enable_qa_for_this_course;
$is_enrolled                  = tutor_utils()->is_enrolled( $course_id );
$is_instructor_of_this_course = tutor_utils()->has_user_course_content_access( $user_id, $course_id );
$is_user_admin                = current_user_can( 'administrator' );
$is_public_course             = \TUTOR\Course_List::is_public( $course_id );
?>

<?php do_action( 'tutor_lesson/single/before/lesson_sidebar' ); ?>
<div class="tutor-course-single-sidebar-title tutor-d-flex tutor-justify-between" data-course-id="<?php echo esc_attr( $course_id ); ?>">
	<span class="tutor-d-flex tutor-align-center tutor-w-100 tutor-position-relative">
		<input type="text" class="tutor-form-control tutor-form-control-sm" placeholder="<?php esc_attr_e( 'Ders ara...', 'tutor' ); ?>" id="tutor-course-content-search">
		<i class="tutor-icon-search tutor-search-icon" id="tutor-course-search-icon"></i>
	</span>
	<span class="tutor-d-block tutor-d-xl-none">
		<a href="#" class="tutor-iconic-btn" tutor-hide-course-single-sidebar>
			<span class="tutor-icon-times" area-hidden="true"></span>
		</a>
	</span>
</div>

<?php
$topics = tutor_utils()->get_topics( $course_id );
if ( $topics->have_posts() ) {
	// İkinci topic varsayılan olarak açık olmasın
	// $active_topic = 2; // İkinci topic aktif olsun

	// Loop through topics.
	while ( $topics->have_posts() ) {
		$topics->the_post();
		$topic_id        = get_the_ID();
		$topic_summery   = get_the_content();
		// Kullanıcı bazlı tamamlanma sayılarını al
		$total_contents  = tutor_utils()->count_completed_contents_by_topic( $topic_id, $user_id );
		// Eğer kullanıcı bazlı hesaplama başarısız olursa genel hesaplama yap
		if ( ! $total_contents || ! isset( $total_contents['contents'] ) ) {
			$total_contents = tutor_utils()->count_completed_contents_by_topic( $topic_id );
		}
		$lessons         = tutor_utils()->get_course_contents_by_topic( get_the_ID(), -1 );
		$lessons         = apply_filters( 'tutor_filter_lesson_sidebar', $lessons, $topic_id );

		// Sadece mevcut içerik bu topic içindeyse açık olsun, sabit index değeri kullanmıyoruz
		$index = isset($index) ? $index + 1 : 1;
		$is_content_in_topic = !empty(
			array_filter(
				$lessons->posts,
				function ( $content ) use ( $current_post ) {
					return $content->ID == $current_post->ID;
				}
			)
		);
		$is_topic_active = $is_content_in_topic; // Sadece içerik bu topic içindeyse açık olsun
		?>
		<div class="tutor-course-topic tutor-course-topic-<?php echo esc_attr( $topic_id ); ?>">
			<div class="tutor-accordion-item-header<?php echo $is_topic_active ? ' is-active' : ''; ?>" tutor-course-single-topic-toggler>
				<div class="tutor-row tutor-gx-1">
					<div class="tutor-col">
						<div class="tutor-course-topic-title">
							<?php the_title(); ?>
						</div>
					</div>

					<div class="tutor-col-auto tutor-align-self-center">
						<?php if ( isset( $total_contents['contents'] ) && $total_contents['contents'] > 0 ) : ?>
							<div class="tutor-course-topic-summary tutor-pl-8">
								<?php echo esc_html( isset( $total_contents['completed'] ) ? $total_contents['completed'] : 0 ); ?>/<?php echo esc_html( isset( $total_contents['contents'] ) ? $total_contents['contents'] : 0 ); ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			</div>

			<div class="tutor-accordion-item-body <?php echo $is_topic_active ? '' : 'tutor-display-none'; ?>">
				<?php if ( isset( $total_contents['contents'] ) && $total_contents['contents'] > 0 ) : ?>
					<div class="tutor-course-topic-summary-top tutor-d-flex tutor-justify-start tutor-py-8 tutor-my-8 tutor-pl-16">
						<?php if ( trim( $topic_summery ) ) : ?>
							<div class="tutor-course-topic-description tutor-color-secondary tutor-fs-7">
								<?php echo esc_textarea( $topic_summery ); ?>
							</div>
						<?php endif; ?>

					</div>
				<?php endif; ?>
				<?php
				do_action( 'tutor/lesson_list/before/topic', $topic_id );

				// Loop through lesson, quiz, assignment, zoom lesson.
				while ( $lessons->have_posts() ) {
					$lessons->the_post();

					$show_permalink = ! $_is_preview || $is_enrolled || get_post_meta( $post->ID, '_is_preview', true ) || $is_public_course || $is_instructor_of_this_course;
					$show_permalink = apply_filters( 'tutor_course/single/content/show_permalink', $show_permalink, get_the_ID() );
					$lock_icon      = ! $show_permalink;
					$show_permalink = null === $show_permalink ? true : $show_permalink;

					if ( 'tutor_quiz' === $post->post_type ) {
						$quiz = $post;
						?>
						<div class="tutor-course-topic-item tutor-course-topic-item-quiz<?php echo ( get_the_ID() == $current_post->ID ) ? ' is-active' : ''; ?>" data-quiz-id="<?php echo esc_attr( $quiz->ID ); ?>">
							<a href="<?php echo $show_permalink ? esc_url( get_permalink( $quiz->ID ) ) : '#'; ?>" data-quiz-id="<?php echo esc_attr( $quiz->ID ); ?>">
								<div class="tutor-d-flex tutor-mr-32">
									<?php
									// Kapak fotoğrafını al
									$thumbnail_id = get_post_thumbnail_id($quiz->ID);

									if ($thumbnail_id) {
										$thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'thumbnail');
										if ($thumbnail_url && is_array($thumbnail_url)) {
											echo '<img src="' . esc_url($thumbnail_url[0]) . '" alt="' . get_the_title() . '" class="tutor-course-topic-item-thumbnail">';
										} else {
											// Kapak fotoğrafı yoksa mavi gradient göster
											echo '<div class="tutor-course-topic-item-gradient"></div>';
										}
									} else {
										// Kapak fotoğrafı yoksa mavi gradient göster
										echo '<div class="tutor-course-topic-item-gradient"></div>';
									}
									?>
									<div class="tutor-lesson-content-title-wrap">
										<span class="tutor-course-topic-item-title tutor-fs-7 tutor-fw-medium">
											<?php echo esc_html( $quiz->post_title ); ?>
										</span>
										<?php
										$time_limit   = (int) tutor_utils()->get_quiz_option( $quiz->ID, 'time_limit.time_value' );

										if ( $time_limit ) {
											$time_type = tutor_utils()->get_quiz_option( $quiz->ID, 'time_limit.time_type' );

											$time_multipliers = array(
												'minutes' => MINUTE_IN_SECONDS,
												'hours'   => HOUR_IN_SECONDS,
												'days'    => DAY_IN_SECONDS,
												'weeks'   => WEEK_IN_SECONDS,
											);

											if ( isset( $time_multipliers[ $time_type ] ) ) {
												$time_limit *= $time_multipliers[ $time_type ];
											}

											$hours          = floor( $time_limit / HOUR_IN_SECONDS );
											$minutes        = floor( ( $time_limit % HOUR_IN_SECONDS ) / MINUTE_IN_SECONDS );
											$seconds        = $time_limit % MINUTE_IN_SECONDS;
											$formatted_time = sprintf( '%02d:%02d:%02d', $hours, $minutes, $seconds );

											$markup = '<span class="tutor-course-topic-item-duration tutor-fs-7 tutor-fw-medium tutor-color-muted">' . $formatted_time . '</span>';
											echo wp_kses(
												$markup,
												array(
													'span' => array( 'class' => true ),
												)
											);
										} else {
											// Süre yoksa "Sınav" göster
											$markup = '<span class="tutor-course-topic-item-duration tutor-fs-7 tutor-fw-medium tutor-color-muted">Sınav</span>';
											echo wp_kses(
												$markup,
												array(
													'span' => array( 'class' => true ),
												)
											);
										}
										?>
									</div>
								</div>
								<div class="tutor-d-flex tutor-ml-auto tutor-flex-shrink-0">
									<?php
									$last_attempt = ( new QuizModel() )->get_first_or_last_attempt( $quiz->ID );

									$attempt_ended = is_object( $last_attempt ) && QuizModel::ATTEMPT_STARTED !== $last_attempt->attempt_status;
									$result_class  = '';

									$quiz_result = QuizModel::get_quiz_result( $quiz->ID );
									if ( $attempt_ended && QuizModel::ATTEMPT_STARTED !== $last_attempt->attempt_status ) {
										if ( 'fail' === $quiz_result ) {
											$result_class = 'tutor-check-fail';
										}
										if ( 'pending' === $quiz_result ) {
											$result_class = 'tutor-check-pending';
										}
									}
									?>

									<?php if ( ! $lock_icon ) : ?>
										<input 	type="checkbox"
												class="tutor-form-check-input tutor-form-check-circle <?php echo esc_attr( $result_class ); ?>"
												disabled="disabled"
												readonly="readonly"
												<?php echo esc_attr( $attempt_ended ? 'checked="checked"' : '' ); ?> />
									<?php else : ?>
										<i class="tutor-icon-lock-line tutor-fs-7 tutor-color-muted tutor-mr-4" area-hidden="true"></i>
									<?php endif; ?>
								</div>
							</a>
						</div>
					<?php } elseif ( 'tutor_assignments' === $post->post_type ) { ?>
						<div class="tutor-course-topic-item tutor-course-topic-item-assignment<?php echo esc_attr( get_the_ID() == $current_post->ID ? ' is-active' : '' ); ?>">
							<a href="<?php echo $show_permalink ? esc_url( get_permalink( $post->ID ) ) : '#'; ?>" data-assignment-id="<?php echo esc_attr( $post->ID ); ?>">
								<div class="tutor-d-flex tutor-mr-32">
									<?php
									// Kapak fotoğrafını al
									$thumbnail_id = get_post_thumbnail_id($post->ID);

									if ($thumbnail_id) {
										$thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'thumbnail');
										if ($thumbnail_url && is_array($thumbnail_url)) {
											echo '<img src="' . esc_url($thumbnail_url[0]) . '" alt="' . get_the_title() . '" class="tutor-course-topic-item-thumbnail">';
										} else {
											// Kapak fotoğrafı yoksa mavi gradient göster
											echo '<div class="tutor-course-topic-item-gradient"></div>';
										}
									} else {
										// Kapak fotoğrafı yoksa mavi gradient göster
										echo '<div class="tutor-course-topic-item-gradient"></div>';
									}
									?>
									<div class="tutor-lesson-content-title-wrap">
										<span class="tutor-course-topic-item-title tutor-fs-7 tutor-fw-medium">
											<?php echo esc_html( $post->post_title ); ?>
										</span>
									</div>
								</div>
								<div class="tutor-d-flex tutor-ml-auto tutor-flex-shrink-0">
									<?php if ( $show_permalink ) : ?>
										<?php do_action( 'tutor/assignment/right_icon_area', $post, $lock_icon ); ?>
									<?php else : ?>
										<i class="tutor-icon-lock-line tutor-fs-7 tutor-color-muted tutor-mr-4" area-hidden="true"></i>
									<?php endif; ?>
								</div>
							</a>
						</div>
					<?php } elseif ( 'tutor_zoom_meeting' === $post->post_type ) { ?>
						<div class="tutor-course-topic-item tutor-course-topic-item-zoom<?php echo esc_attr( ( get_the_ID() == $current_post->ID ) ? ' is-active' : '' ); ?>">
							<a href="<?php echo $show_permalink ? esc_url( get_permalink( $post->ID ) ) : '#'; ?>">
								<div class="tutor-d-flex tutor-mr-32">
									<?php
									// Kapak fotoğrafını al
									$thumbnail_id = get_post_thumbnail_id($post->ID);

									if ($thumbnail_id) {
										$thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'thumbnail');
										if ($thumbnail_url && is_array($thumbnail_url)) {
											echo '<img src="' . esc_url($thumbnail_url[0]) . '" alt="' . get_the_title() . '" class="tutor-course-topic-item-thumbnail">';
										} else {
											// Kapak fotoğrafı yoksa mavi gradient göster
											echo '<div class="tutor-course-topic-item-gradient"></div>';
										}
									} else {
										// Kapak fotoğrafı yoksa mavi gradient göster
										echo '<div class="tutor-course-topic-item-gradient"></div>';
									}
									?>
									<div class="tutor-lesson-content-title-wrap">
										<span class="tutor-course-topic-item-title tutor-fs-7 tutor-fw-medium">
											<?php echo esc_html( $post->post_title ); ?>
										</span>
									</div>
								</div>
								<div class="tutor-d-flex tutor-ml-auto tutor-flex-shrink-0">
									<?php if ( $show_permalink ) : ?>
										<?php do_action( 'tutor/zoom/right_icon_area', $post->ID, $lock_icon ); ?>
									<?php else : ?>
										<i class="tutor-icon-lock-line tutor-fs-7 tutor-color-muted tutor-mr-4" area-hidden="true"></i>
									<?php endif; ?>
								</div>
							</a>
						</div>
					<?php } elseif ( 'tutor-google-meet' === $post->post_type ) { ?>
						<div class="tutor-course-topic-item tutor-course-topic-item-zoom<?php echo esc_attr( get_the_ID() == $current_post->ID ? ' is-active' : '' ); ?>">
							<a href="<?php echo $show_permalink ? esc_url( get_permalink( $post->ID ) ) : '#'; ?>">
								<div class="tutor-d-flex tutor-mr-32">
									<?php
									// Kapak fotoğrafını al
									$thumbnail_id = get_post_thumbnail_id($post->ID);

									if ($thumbnail_id) {
										$thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'thumbnail');
										if ($thumbnail_url && is_array($thumbnail_url)) {
											echo '<img src="' . esc_url($thumbnail_url[0]) . '" alt="' . get_the_title() . '" class="tutor-course-topic-item-thumbnail">';
										} else {
											// Kapak fotoğrafı yoksa mavi gradient göster
											echo '<div class="tutor-course-topic-item-gradient"></div>';
										}
									} else {
										// Kapak fotoğrafı yoksa mavi gradient göster
										echo '<div class="tutor-course-topic-item-gradient"></div>';
									}
									?>
									<div class="tutor-lesson-content-title-wrap">
										<span class="tutor-course-topic-item-title tutor-fs-7 tutor-fw-medium">
											<?php echo esc_html( $post->post_title ); ?>
										</span>
									</div>
								</div>
								<div class="tutor-d-flex tutor-ml-auto tutor-flex-shrink-0">
									<?php if ( $show_permalink ) : ?>
										<?php do_action( 'tutor/google_meet/right_icon_area', $post->ID, false ); ?>
									<?php else : ?>
										<i class="tutor-icon-lock-line tutor-fs-7 tutor-color-muted tutor-mr-4" area-hidden="true"></i>
									<?php endif; ?>
								</div>
							</a>
						</div>
					<?php } else { ?>

						<?php
						$video     = tutor_utils()->get_video_info();
						$play_time = false;
						if ( $video ) {
							$play_time = $video->playtime;
						}
						$is_completed_lesson = tutor_utils()->is_completed_lesson();
						?>
						<?php
						// Süre varsa video sınıfı ekle
						$video_class = $play_time ? ' tutor-course-topic-item-has-video' : '';
						?>
						<div class="tutor-course-topic-item tutor-course-topic-item-lesson<?php echo esc_attr( get_the_ID() == $current_post->ID ? ' is-active' : '' ); ?><?php echo esc_attr($video_class); ?>">
							<a href="<?php echo $show_permalink ? esc_url( get_the_permalink() ) : '#'; ?>" data-lesson-id="<?php the_ID(); ?>">
								<div class="tutor-d-flex tutor-mr-32">
									<?php
									// Kapak fotoğrafını al
									$thumbnail_id = get_post_thumbnail_id($post->ID);

									if ($thumbnail_id) {
										$thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'thumbnail');
										if ($thumbnail_url && is_array($thumbnail_url)) {
											echo '<img src="' . esc_url($thumbnail_url[0]) . '" alt="' . get_the_title() . '" class="tutor-course-topic-item-thumbnail">';
										} else {
											// Kapak fotoğrafı yoksa mavi gradient göster
											echo '<div class="tutor-course-topic-item-gradient"></div>';
										}
									} else {
										// Kapak fotoğrafı yoksa mavi gradient göster
										echo '<div class="tutor-course-topic-item-gradient"></div>';
									}
									?>
									<div class="tutor-lesson-content-title-wrap">
										<span class="tutor-course-topic-item-title tutor-fs-7 tutor-fw-medium">
											<?php the_title(); ?>
										</span>
										<?php
										if ( $play_time ) {
											$markup = "<span class='tutor-course-topic-item-duration tutor-fs-7 tutor-fw-medium tutor-color-muted'>" . tutor_utils()->get_optimized_duration( $play_time ) . '</span>';
											echo wp_kses(
												$markup,
												array(
													'span' => array( 'class' => true ),
												)
											);
										} else {
											// Video yüklenmemiş ise "Yazı Metni" göster
											$markup = "<span class='tutor-course-topic-item-duration tutor-fs-7 tutor-fw-medium tutor-color-muted'>Yazı Metni</span>";
											echo wp_kses(
												$markup,
												array(
													'span' => array( 'class' => true ),
												)
											);
										}
										?>
									</div>
								</div>

								<div class="tutor-d-flex tutor-ml-auto tutor-flex-shrink-0">
									<?php
									$lesson_complete_icon = $is_completed_lesson ? 'checked' : '';

									if ( ! $lock_icon ) {
										$markup = "<input $lesson_complete_icon type='checkbox' class='tutor-form-check-input tutor-form-check-circle' disabled readonly />";
										echo wp_kses(
											$markup,
											array(
												'input' => array(
													'checked' => true,
													'class' => true,
													'type' => true,
													'disabled' => true,
													'readonly' => true,
												),
											)
										);
									} else {
										$markup = '<i class="tutor-icon-lock-line tutor-fs-7 tutor-color-muted tutor-mr-4" area-hidden="true"></i>';
										echo wp_kses(
											$markup,
											array(
												'i' => array(
													'class' => true,
													'area-hidden' => true,
												),
											)
										);
									}
									?>
								</div>
							</a>
						</div>
						<?php
					}
				}
				$lessons->reset_postdata();
				do_action( 'tutor/lesson_list/after/topic', $topic_id );
				?>
			</div>
		</div>
		<?php
	}
	$topics->reset_postdata();
	wp_reset_postdata();
}
?>
<?php do_action( 'tutor_lesson/single/after/lesson_sidebar' ); ?>
