/**
 * Custom Theme Mode Button CSS
 * <PERSON><PERSON> dos<PERSON>, tema modu değiştirme butonu için stil ve animasyonları içerir.
 *
 * @package DmrLMS
 * @since 1.0.5
 */

/* Tema modu geçiş butonu konteyneri */
.tutor-theme-mode-toggle {
    display: flex;
    align-items: center;
    margin-right: 15px;
    position: relative;
    cursor: pointer;
}

/* Tema modu buton stili */
.tutor-theme-mode-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--tutor-color-primary);
}

.tutor-theme-mode-button:hover .tutor-theme-mode-icon {
    transform: scale(1.2);
}

/* Tema modu buton ikonları */
.tutor-theme-mode-icon {
    width: 24px;
    height: 24px;
    stroke: var(--tutor-color-primary);
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: transform 0.3s ease;
}

/* <PERSON><PERSON>neş ikonu - gündüz modu */
.tutor-theme-mode-icon-sun {
    display: none;
}

/* Hilal ikonu - gece modu */
.tutor-theme-mode-icon-moon {
    display: none;
}

/* Gündüz modunda güneş ikonunu göster */
html:not([data-theme="dark"]) .tutor-theme-mode-icon-sun {
    display: block;
}

/* Gece modunda hilal ikonunu göster */
html[data-theme="dark"] .tutor-theme-mode-icon-moon {
    display: block;
}

/* Tema modu geçiş butonu - animasyon */
@keyframes theme-toggle-scale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1);
    }
}

/* Tablet cihazlar için hover efektini kaldır */
@media (min-width: 768px) and (max-width: 1024px) {
    .tutor-theme-mode-button:hover .tutor-theme-mode-icon {
        transform: none;
    }
}

/* Mobil cihazlar için özel ayarlar */
@media (max-width: 767px) {
    /* iPhone ve küçük mobil cihazlarda buton boyutunu artır */
    .tutor-theme-mode-toggle {
        margin-right: 10px;
    }

    .tutor-theme-mode-button {
        width: 44px;
        height: 44px;
        /* Dokunmatik kullanım için daha büyük tıklanabilir alan */
        padding: 8px;
    }

    /* Mobil cihazlarda hover efektini kaldır */
    .tutor-theme-mode-button:hover .tutor-theme-mode-icon {
        transform: none;
    }

    .tutor-theme-mode-icon {
        width: 26px;
        height: 26px;
        stroke-width: 2.2;
    }
}

/* Çok küçük ekranlar için (iPhone SE gibi) */
@media (max-width: 375px) {
    .tutor-theme-mode-button {
        width: 42px;
        height: 42px;
    }

    .tutor-theme-mode-icon {
        width: 24px;
        height: 24px;
    }
}
