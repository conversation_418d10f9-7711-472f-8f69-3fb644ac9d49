/**
 * Tutor LMS Renk Değişkenleri
 *
 * Bu script, admin panelinden ayarlanan ana renk değişkenlerinden
 * türetilmiş renk değişkenlerini hesaplar ve CSS değişkenleri olarak ekler.
 * Modern dashboard için geliştirilmiş renk hesaplamaları içerir.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Ana renk değişkenlerini al
    const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--tutor-color-primary').trim() || '#4361ee';
    const secondaryColor = getComputedStyle(document.documentElement).getPropertyValue('--tutor-color-secondary').trim() || '#7209b7';

    // Modern dashboard için varsayılan renkleri ayarla
    if (!getComputedStyle(document.documentElement).getPropertyValue('--tutor-primary').trim()) {
        document.documentElement.style.setProperty('--tutor-primary', primaryColor);
        document.documentElement.style.setProperty('--tutor-secondary', secondaryColor);
    }

    if (primaryColor) {
        // RGB formatına dönüştür
        const rgb = hexToRgb(primaryColor);

        if (rgb) {
            // Türetilmiş renk değişkenlerini hesapla ve CSS'e ekle
            document.documentElement.style.setProperty('--tutor-light-primary', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.05)`);
            document.documentElement.style.setProperty('--tutor-medium-primary', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`);
            document.documentElement.style.setProperty('--tutor-primary-rgb', `${rgb.r}, ${rgb.g}, ${rgb.b}`);

            // Mobil menü arka plan rengini ana renkle eşleştir
            document.documentElement.style.setProperty('--tutor-mobile-header-bg', primaryColor);

            // Modern dashboard için ek renk değişkenleri
            document.documentElement.style.setProperty('--tutor-primary-hover', adjustColor(primaryColor, -15));
            document.documentElement.style.setProperty('--tutor-primary-light', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`);
            document.documentElement.style.setProperty('--tutor-primary-lighter', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.05)`);
        }
    }

    if (secondaryColor) {
        // RGB formatına dönüştür
        const rgb = hexToRgb(secondaryColor);

        if (rgb) {
            // İkincil renk için türetilmiş değişkenler
            document.documentElement.style.setProperty('--tutor-secondary-rgb', `${rgb.r}, ${rgb.g}, ${rgb.b}`);
            document.documentElement.style.setProperty('--tutor-secondary-light', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`);
        }
    }

    /**
     * Hex renk kodunu RGB formatına dönüştürür
     * @param {string} hex - Hex renk kodu (#RRGGBB formatında)
     * @return {object|null} - {r, g, b} formatında RGB değerleri veya null
     */
    function hexToRgb(hex) {
        // # işaretini kaldır
        hex = hex.replace(/^#/, '');

        // Kısa hex formatını (3 karakter) uzun formata (6 karakter) dönüştür
        if (hex.length === 3) {
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
        }

        // Hex değerini RGB'ye dönüştür
        const bigint = parseInt(hex, 16);
        if (isNaN(bigint)) return null;

        return {
            r: (bigint >> 16) & 255,
            g: (bigint >> 8) & 255,
            b: bigint & 255
        };
    }

    /**
     * Rengi belirtilen miktarda aydınlatır veya koyulaştırır
     * @param {string} color - Hex renk kodu
     * @param {number} amount - Aydınlatma/koyulaştırma miktarı (-100 ile 100 arası)
     * @return {string} - Yeni hex renk kodu
     */
    function adjustColor(color, amount) {
        const rgb = hexToRgb(color);
        if (!rgb) return color;

        // Her renk kanalını ayarla
        let r = rgb.r + amount;
        let g = rgb.g + amount;
        let b = rgb.b + amount;

        // Sınırları kontrol et
        r = Math.max(0, Math.min(255, r));
        g = Math.max(0, Math.min(255, g));
        b = Math.max(0, Math.min(255, b));

        // RGB'yi hex'e dönüştür
        return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    // Dashboard ile ilgili kod custom-dashboard-page.js dosyasına taşındı
});
