<?php
/**
 * Student Course Assignment
 *
 * @package Tutor\Student
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Student Course Assignment class
 *
 * @since 1.0.0
 */
class Student_Course_Assignment {

	/**
	 * Constructor
	 */
	public function __construct() {
		// Register scripts
		add_action( 'admin_enqueue_scripts', array( $this, 'register_scripts' ) );

		// We're directly adding the button to the students.php template

		// AJAX handlers
		add_action( 'wp_ajax_tutor_get_courses_for_assignment', array( $this, 'get_courses_for_assignment' ) );
		add_action( 'wp_ajax_tutor_get_student_enrolled_courses', array( $this, 'get_student_enrolled_courses' ) );
		add_action( 'wp_ajax_tutor_assign_course_to_student', array( $this, 'assign_course_to_student' ) );
		add_action( 'wp_ajax_tutor_unassign_course_from_student', array( $this, 'unassign_course_from_student' ) );
		add_action( 'wp_ajax_tutor_get_student_course_count', array( $this, 'get_student_course_count' ) );
	}

	/**
	 * Register scripts
	 *
	 * @return void
	 */
	public function register_scripts() {
		$page = isset( $_GET['page'] ) ? sanitize_text_field( $_GET['page'] ) : '';

		// Only load on students page
		if ( 'tutor-students' === $page ) {
			wp_enqueue_style( 'tutor-student-course-assignment', plugin_dir_url( __FILE__ ) . '../css/student-course-assignment.css', array(), time() );
			wp_enqueue_script( 'tutor-student-course-assignment', plugin_dir_url( __FILE__ ) . '../js/student-course-assignment.js', array( 'jquery' ), time(), true );

			// Localize script
			wp_localize_script(
				'tutor-student-course-assignment',
				'tutor_student_course_assignment',
				array(
					'nonce' => wp_create_nonce( 'tutor_student_course_assignment_nonce' ),
					'i18n'  => array(
						'assign_course' => __( 'Kurs Tanımla', 'tutor' ),
						'search_courses' => __( 'Kurs ara...', 'tutor' ),
						'loading' => __( 'Yükleniyor...', 'tutor' ),
						'error' => __( 'Bir hata oluştu', 'tutor' ),
						'no_courses' => __( 'Kurs bulunamadı', 'tutor' ),
						'assign' => __( 'Tanımla', 'tutor' ),
						'unassign' => __( 'Çıkar', 'tutor' ),
						'processing' => __( 'İşleniyor...', 'tutor' ),
						'order_created' => __( 'Sipariş oluşturuldu', 'tutor' ),
						'view_order' => __( 'Siparişi Görüntüle', 'tutor' ),
					),
				)
			);
		}
	}



	/**
	 * Get courses for assignment
	 *
	 * @return void
	 */
	public function get_courses_for_assignment() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'tutor_student_course_assignment_nonce' ) ) {
			wp_send_json_error( __( 'Nonce verification failed', 'tutor' ) );
		}

		// Check if user is admin
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'Permission denied', 'tutor' ) );
		}

		// Get courses
		$args = array(
			'post_type'      => tutor()->course_post_type,
			'post_status'    => 'publish',
			'posts_per_page' => -1,
		);

		$courses = get_posts( $args );

		if ( empty( $courses ) ) {
			wp_send_json_error( __( 'No courses found', 'tutor' ) );
		}

		// Add thumbnail URLs to courses
		$courses_with_thumbnails = array();
		foreach ( $courses as $course ) {
			$thumbnail_id = get_post_thumbnail_id( $course->ID );
			$thumbnail_url = $thumbnail_id ? wp_get_attachment_image_url( $thumbnail_id, 'thumbnail' ) : '';

			// Add thumbnail URL to course object
			$course->thumbnail_url = $thumbnail_url;
			$courses_with_thumbnails[] = $course;
		}

		wp_send_json_success( $courses_with_thumbnails );
	}

	/**
	 * Get student enrolled courses
	 *
	 * @return void
	 */
	public function get_student_enrolled_courses() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'tutor_student_course_assignment_nonce' ) ) {
			wp_send_json_error( __( 'Nonce verification failed', 'tutor' ) );
		}

		// Check if user is admin
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'Permission denied', 'tutor' ) );
		}

		$student_id = isset( $_POST['student_id'] ) ? intval( $_POST['student_id'] ) : 0;

		if ( ! $student_id ) {
			wp_send_json_error( __( 'Invalid student ID', 'tutor' ) );
		}

		// Get enrolled courses
		$enrolled_courses = tutor_utils()->get_enrolled_courses_ids_by_user( $student_id );

		// Make sure we return an array
		if ( ! is_array( $enrolled_courses ) ) {
			$enrolled_courses = array();
		}

		wp_send_json_success( $enrolled_courses );
	}

	/**
	 * Assign course to student
	 *
	 * @return void
	 */
	public function assign_course_to_student() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'tutor_student_course_assignment_nonce' ) ) {
			wp_send_json_error( __( 'Nonce verification failed', 'tutor' ) );
		}

		// Check if user is admin
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'Permission denied', 'tutor' ) );
		}

		$course_id = isset( $_POST['course_id'] ) ? intval( $_POST['course_id'] ) : 0;
		$student_id = isset( $_POST['student_id'] ) ? intval( $_POST['student_id'] ) : 0;

		if ( ! $course_id || ! $student_id ) {
			wp_send_json_error( __( 'Invalid course or student ID', 'tutor' ) );
		}

		// Check if already enrolled
		$is_enrolled = tutor_utils()->is_enrolled( $course_id, $student_id );

		if ( $is_enrolled ) {
			wp_send_json_error( __( 'Student is already enrolled in this course', 'tutor' ) );
		}

		// Check if course is purchasable (paid)
		$is_paid_course = tutor_utils()->is_course_purchasable( $course_id );
		$monetize_by = tutor_utils()->get_option( 'monetize_by' );

		// If it's a paid course and monetized by WooCommerce
		if ( $is_paid_course && 'wc' === $monetize_by ) {
			// Create a WooCommerce order for the student
			$product_id = tutor_utils()->get_course_product_id( $course_id );

			if ( $product_id && class_exists( 'WooCommerce' ) ) {
				// Create a new order
				$order = wc_create_order();

				// Add the course product to the order
				$order->add_product( wc_get_product( $product_id ), 1 );

				// Set the customer
				$order->set_customer_id( $student_id );

				// Calculate totals
				$order->calculate_totals();

				// Set order status to completed for immediate access
				$order->update_status( 'completed', __( 'Manual Enrollment Order', 'tutor' ) );

				// Make sure WooCommerce hooks are triggered
				do_action( 'woocommerce_order_status_completed', $order->get_id() );
				do_action( 'woocommerce_payment_complete', $order->get_id() );

				// Get the order ID
				$order_id = $order->get_id();

				// Enroll student with the order ID and make sure enrollment is completed
				// Add filter to set enrollment status to completed
				add_filter(
					'tutor_enroll_data',
					function ( $data ) {
						$data['post_status'] = 'completed';
						return $data;
					}
				);
				$enroll = tutor_utils()->do_enroll( $course_id, $order_id, $student_id );

				// Ensure enrollment status is completed
				if ($enroll) {
					global $wpdb;
					$wpdb->update(
						$wpdb->posts,
						array('post_status' => 'completed'),
						array('ID' => $enroll)
					);

					// Trigger enrollment completion hooks
					do_action('tutor_after_enrolled', $course_id, $student_id, $enroll);
				}

				if ( $enroll ) {
					wp_send_json_success(
						array(
							'message' => __( 'Student enrolled successfully. WooCommerce order created.', 'tutor' ),
							'order_id' => $order_id,
							'is_paid' => true
						)
					);
				} else {
					// If enrollment fails, delete the order
					wp_delete_post( $order_id, true );
					wp_send_json_error( __( 'Failed to enroll student', 'tutor' ) );
				}
			} else {
				wp_send_json_error( __( 'Product not found or WooCommerce not active', 'tutor' ) );
			}
		} else {
			// For free courses, just enroll the student directly
			// Add filter to ensure enrollment status is completed
			add_filter(
				'tutor_enroll_data',
				function ( $data ) {
					$data['post_status'] = 'completed';
					return $data;
				}
			);
			$enroll = tutor_utils()->do_enroll( $course_id, 0, $student_id );

			// Double check enrollment status
			if ($enroll) {
				global $wpdb;
				$wpdb->update(
					$wpdb->posts,
					array('post_status' => 'completed'),
					array('ID' => $enroll)
				);

				// Trigger enrollment completion hooks
				do_action('tutor_after_enrolled', $course_id, $student_id, $enroll);
			}

			if ( $enroll ) {
				wp_send_json_success(
					array(
						'message' => __( 'Student enrolled successfully', 'tutor' ),
						'is_paid' => false
					)
				);
			} else {
				wp_send_json_error( __( 'Failed to enroll student', 'tutor' ) );
			}
		}
	}

	/**
	 * Unassign course from student
	 *
	 * @return void
	 */
	public function unassign_course_from_student() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'tutor_student_course_assignment_nonce' ) ) {
			wp_send_json_error( __( 'Nonce verification failed', 'tutor' ) );
		}

		// Check if user is admin
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'Permission denied', 'tutor' ) );
		}

		$course_id = isset( $_POST['course_id'] ) ? intval( $_POST['course_id'] ) : 0;
		$student_id = isset( $_POST['student_id'] ) ? intval( $_POST['student_id'] ) : 0;

		if ( ! $course_id || ! $student_id ) {
			wp_send_json_error( __( 'Invalid course or student ID', 'tutor' ) );
		}

		// Check if enrolled
		$is_enrolled = tutor_utils()->is_enrolled( $course_id, $student_id );

		if ( ! $is_enrolled ) {
			wp_send_json_error( __( 'Student is not enrolled in this course', 'tutor' ) );
		}

		// Get enrollment ID
		global $wpdb;
		$enrollment_id = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT ID FROM {$wpdb->posts}
				WHERE post_type = %s
				AND post_parent = %d
				AND post_author = %d
				AND post_status = %s",
				'tutor_enrolled',
				$course_id,
				$student_id,
				'completed'
			)
		);

		if ( ! $enrollment_id ) {
			wp_send_json_error( __( 'Enrollment not found', 'tutor' ) );
		}

		// Cancel enrollment
		$wpdb->update(
			$wpdb->posts,
			array( 'post_status' => 'cancel' ),
			array( 'ID' => $enrollment_id )
		);

		// Run action hook
		do_action( 'tutor_enrollment/after/cancel', $enrollment_id );

		wp_send_json_success( __( 'Enrollment cancelled successfully', 'tutor' ) );
	}

	/**
	 * Get student course count
	 *
	 * @return void
	 */
	public function get_student_course_count() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'tutor_student_course_assignment_nonce' ) ) {
			wp_send_json_error( __( 'Nonce verification failed', 'tutor' ) );
		}

		// Check if user is admin
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'Permission denied', 'tutor' ) );
		}

		$student_id = isset( $_POST['student_id'] ) ? intval( $_POST['student_id'] ) : 0;

		if ( ! $student_id ) {
			wp_send_json_error( __( 'Invalid student ID', 'tutor' ) );
		}

		// Get enrolled courses
		$enrolled_courses = tutor_utils()->get_enrolled_courses_ids_by_user( $student_id );
		$count = is_array( $enrolled_courses ) ? count( $enrolled_courses ) : 0;

		wp_send_json_success( $count );
	}
}

// Initialize the class
new Student_Course_Assignment();
