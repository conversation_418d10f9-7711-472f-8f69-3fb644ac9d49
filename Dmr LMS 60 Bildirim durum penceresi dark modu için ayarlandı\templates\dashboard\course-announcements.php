<?php
/**
 * Template for displaying Course Announcements for enrolled courses
 *
 * @package DmrLMS\Templates
 * @subpackage Dashboard
 * <AUTHOR> Developer
 * @since 1.0.6
 */

use TUTOR\Input;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Güvenlik kontrolü - kullanıcı giriş yapmış mı?
if ( ! is_user_logged_in() ) {
	tutor_utils()->tutor_empty_state( __( 'Giri<PERSON> Yapın', 'dmr-lms' ), __( 'Kurs duyurularını görüntülemek için giriş yapmanız gerekiyor.', 'dmr-lms' ) );
	return;
}

$per_page = tutor_utils()->get_option( 'pagination_per_page', 10 );
$paged    = max( 1, Input::get( 'current_page', 1, Input::TYPE_INT ) );

$order_filter  = Input::get( 'order', 'DESC' );
$search_filter = Input::get( 'search', '' );
$course_filter = Input::get( 'course-id', '' );
$date_filter   = Input::get( 'date', '' );

// Kullanıcının kayıtlı olduğu kursları al
$user_id = get_current_user_id();
$enrolled_course_ids = tutor_utils()->get_enrolled_courses_ids_by_user( $user_id );

if ( empty( $enrolled_course_ids ) ) {
	$enrolled_course_ids = array( 0 ); // Boş sonuç için
}

// Tarih filtresi için
$year  = date( 'Y', strtotime( $date_filter ) );
$month = date( 'm', strtotime( $date_filter ) );
$day   = date( 'd', strtotime( $date_filter ) );

// Duyuru sorgusu - sadece kayıtlı olunan kurslardan
$args = array(
	'post_type'      => 'tutor_announcements',
	'post_status'    => 'publish',
	's'              => sanitize_text_field( $search_filter ),
	'post_parent__in' => $enrolled_course_ids, // Sadece kayıtlı olunan kurslar
	'posts_per_page' => sanitize_text_field( $per_page ),
	'paged'          => sanitize_text_field( $paged ),
	'orderby'        => 'date',
	'order'          => sanitize_text_field( $order_filter ),
	'meta_query'     => array(
		'relation' => 'AND',
	),
);

// Belirli bir kurs seçildiyse
if ( ! empty( $course_filter ) && in_array( $course_filter, $enrolled_course_ids ) ) {
	$args['post_parent'] = sanitize_text_field( $course_filter );
	unset( $args['post_parent__in'] );
}

// Tarih filtresi
if ( ! empty( $date_filter ) ) {
	$args['date_query'] = array(
		array(
			'year'  => $year,
			'month' => $month,
			'day'   => $day,
		),
	);
}

$the_query = new WP_Query( $args );

// Kayıtlı olunan kursları dropdown için al
$enrolled_courses = array();
if ( ! empty( $enrolled_course_ids ) ) {
	$enrolled_courses_query = new WP_Query( array(
		'post_type'      => tutor()->course_post_type,
		'post_status'    => array( 'publish', 'private' ),
		'post__in'       => $enrolled_course_ids,
		'posts_per_page' => -1,
		'orderby'        => 'title',
		'order'          => 'ASC',
	) );
	
	if ( $enrolled_courses_query->have_posts() ) {
		$enrolled_courses = $enrolled_courses_query->posts;
	}
	wp_reset_postdata();
}

$image_base = tutor()->url . '/assets/images/';
?>

<div class="dmr-course-announcements">
<div class="tutor-card tutor-p-24 dmr-course-announcements-header">
	<div class="tutor-row tutor-align-lg-center">
		<div class="tutor-col-lg-auto tutor-mb-16 tutor-mb-lg-0">
			<div class="tutor-round-box tutor-p-8">
				<i class="tutor-icon-bullhorn tutor-fs-3" area-hidden="true"></i>
			</div>
		</div>

		<div class="tutor-col tutor-mb-16 tutor-mb-lg-0">
			<div class="tutor-fs-6 tutor-color-muted tutor-mb-4">
				<?php esc_html_e( 'Kurs Duyuruları', 'dmr-lms' ); ?>
			</div>
			<div class="tutor-fs-5 tutor-color-black">
				<?php esc_html_e( 'Kayıtlı olduğunuz kurslardaki tüm duyuruları görüntüleyin', 'dmr-lms' ); ?>
			</div>
		</div>
	</div>
</div>

<div class="tutor-card tutor-p-24 tutor-mt-24 dmr-course-announcements-filters">
	<div class="tutor-row tutor-align-center tutor-mb-16">
		<div class="tutor-col-md-3 tutor-mb-16 tutor-mb-md-0">
			<div class="tutor-form-group tutor-mb-0">
				<select class="tutor-form-select" data-search="course-id">
					<option value=""><?php esc_html_e( 'Tüm Kurslar', 'dmr-lms' ); ?></option>
					<?php if ( ! empty( $enrolled_courses ) ) : ?>
						<?php foreach ( $enrolled_courses as $course ) : ?>
							<option value="<?php echo esc_attr( $course->ID ); ?>" <?php selected( $course_filter, $course->ID ); ?>>
								<?php echo esc_html( $course->post_title ); ?>
							</option>
						<?php endforeach; ?>
					<?php endif; ?>
				</select>
			</div>
		</div>

		<div class="tutor-col-md-3 tutor-mb-16 tutor-mb-md-0">
			<div class="tutor-form-group tutor-mb-0">
				<input type="date" class="tutor-form-control" data-search="date" value="<?php echo esc_attr( $date_filter ); ?>" placeholder="<?php esc_html_e( 'Tarih Seçin', 'dmr-lms' ); ?>">
			</div>
		</div>

		<div class="tutor-col-md-3 tutor-mb-16 tutor-mb-md-0">
			<div class="tutor-form-group tutor-mb-0">
				<input type="search" class="tutor-form-control" data-search="search" value="<?php echo esc_attr( $search_filter ); ?>" placeholder="<?php esc_html_e( 'Duyuru Ara...', 'dmr-lms' ); ?>">
			</div>
		</div>

		<div class="tutor-col-md-3">
			<div class="tutor-form-group tutor-mb-0">
				<select class="tutor-form-select" data-search="order">
					<option value="DESC" <?php selected( $order_filter, 'DESC' ); ?>><?php esc_html_e( 'En Yeni', 'dmr-lms' ); ?></option>
					<option value="ASC" <?php selected( $order_filter, 'ASC' ); ?>><?php esc_html_e( 'En Eski', 'dmr-lms' ); ?></option>
				</select>
			</div>
		</div>
	</div>
</div>

<?php
// Duyuruları listele
$announcements = $the_query->have_posts() ? $the_query->posts : array();

if ( ! empty( $announcements ) ) :
	?>
	<div class="tutor-card tutor-mt-24">
		<div class="tutor-card-header">
			<div class="tutor-fs-6 tutor-fw-medium tutor-color-black">
				<?php
				printf(
					esc_html__( '%d Duyuru Bulundu', 'dmr-lms' ),
					$the_query->found_posts
				);
				?>
			</div>
		</div>

		<div class="tutor-card-body">
			<?php foreach ( $announcements as $announcement ) : ?>
				<?php
				$course = get_post( $announcement->post_parent );
				$author = get_userdata( $announcement->post_author );
				$date_format = get_option( 'date_format' ) . ' ' . get_option( 'time_format' );
				?>
				<div class="tutor-card tutor-announcement-card tutor-mb-24">
					<div class="tutor-card-header tutor-d-block tutor-bg-gray-10">
						<div class="tutor-row tutor-align-center">
							<div class="tutor-col">
								<h3 class="tutor-card-title tutor-fs-6 tutor-fw-medium tutor-mb-4">
									<?php echo esc_html( $announcement->post_title ); ?>
								</h3>
								<div class="dmr-announcement-meta">
									<span>
										<i class="tutor-icon-course"></i>
										<?php echo esc_html( $course ? $course->post_title : __( 'Bilinmeyen Kurs', 'dmr-lms' ) ); ?>
									</span>
									<span>
										<i class="tutor-icon-user"></i>
										<?php echo esc_html( $author ? $author->display_name : __( 'Bilinmeyen Yazar', 'dmr-lms' ) ); ?>
									</span>
									<span>
										<i class="tutor-icon-calendar"></i>
										<?php echo esc_html( date_i18n( $date_format, strtotime( $announcement->post_date ) ) ); ?>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="tutor-card-body">
						<div class="tutor-fs-6 tutor-color-secondary">
							<?php echo wp_kses_post( wpautop( $announcement->post_content ) ); ?>
						</div>
					</div>
				</div>
			<?php endforeach; ?>
		</div>
	</div>

	<?php
	// Pagination
	if ( $the_query->max_num_pages > 1 ) :
		$pagination_data = array(
			'total_items' => $the_query->found_posts,
			'per_page'    => $per_page,
			'paged'       => $paged,
			'base'        => add_query_arg( 'current_page', '%#%' ),
		);

		$pagination_template = tutor()->path . '/views/elements/pagination.php';
		if ( file_exists( $pagination_template ) ) {
			tutor_load_template_from_custom_path( $pagination_template, $pagination_data );
		}
	endif;
	?>

<?php else : ?>
	<div class="tutor-card tutor-mt-24">
		<div class="tutor-card-body dmr-empty-announcements">
			<i class="tutor-icon-bullhorn"></i>
			<h3><?php esc_html_e( 'Henüz Duyuru Yok', 'dmr-lms' ); ?></h3>
			<p><?php esc_html_e( 'Kayıtlı olduğunuz kurslarda henüz duyuru bulunmuyor.', 'dmr-lms' ); ?></p>
		</div>
	</div>
<?php endif; ?>

<script>
jQuery(document).ready(function($) {
	'use strict';

	// Filtre değişikliklerini dinle
	$('[data-search]').on('change input', function() {
		try {
			var searchParams = new URLSearchParams(window.location.search);
			var key = $(this).data('search');
			var value = $(this).val();

			// Değeri sanitize et
			if (value && value.trim()) {
				searchParams.set(key, value.trim());
			} else {
				searchParams.delete(key);
			}

			// Sayfa numarasını sıfırla
			searchParams.delete('current_page');

			// Yeni URL'ye yönlendir
			window.location.search = searchParams.toString();
		} catch (error) {
			console.error('Filtre hatası:', error);
		}
	});

	// Arama inputu için debounce ekle
	var searchTimeout;
	$('[data-search="search"]').off('input').on('input', function() {
		var $this = $(this);
		clearTimeout(searchTimeout);

		searchTimeout = setTimeout(function() {
			$this.trigger('change');
		}, 500); // 500ms bekle
	});
});
</script>
</div> <!-- .dmr-course-announcements -->
