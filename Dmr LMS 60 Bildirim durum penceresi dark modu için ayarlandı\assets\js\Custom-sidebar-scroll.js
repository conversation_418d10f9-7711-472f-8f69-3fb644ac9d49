/**
 * Tutor LMS Sidebar Scroll JavaScript
 * Bu dosya, kurs izleme ekranında sidebar'ın seçilen videoya otomatik scroll yapmasını sağlar.
 * 
 * Özellikler:
 * 1. Sayfa yüklenmeden önce aktif içeriğe scroll yapar
 * 2. Dark mod geçiş stratejilerini kullanır
 * 3. Performans optimizasyonları içerir
 */

(function() {
    'use strict';

    // Sayfa yüklenmeden önce çalışacak kod
    // Bu kısım, sayfa içeriği görünür olmadan önce çalışır
    const scrollToActiveItem = function() {
        // Aktif içerik elementini bul
        const activeItem = document.querySelector('.tutor-course-topic-item.is-active');
        
        // Sidebar elementini bul
        const sidebar = document.querySelector('.tutor-course-single-sidebar-wrapper');
        
        // Eğer aktif içerik ve sidebar varsa
        if (activeItem && sidebar) {
            // Aktif içeriğin pozisyonunu hesapla
            const activeItemTop = activeItem.offsetTop;
            
            // Sidebar'ın yüksekliğini al
            const sidebarHeight = sidebar.clientHeight;
            
            // Aktif içeriği sidebar'ın ortasına getir
            const scrollPosition = activeItemTop - (sidebarHeight / 2);
            
            // Scroll pozisyonunu ayarla (negatif değer olmamasını sağla)
            sidebar.scrollTop = Math.max(0, scrollPosition);
        }
    };

    // Sayfa yüklenmeden önce çalışacak kod
    // Bu kısım, DOM yüklenmeden önce çalışır
    const earlyScrollSetup = function() {
        // Sayfa yüklenmeden önce scroll pozisyonunu ayarlamak için
        // MutationObserver kullanarak DOM değişikliklerini izle
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // Yeni eklenen nodlar içinde sidebar ve aktif içerik var mı kontrol et
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Her eklenen nodu kontrol et
                    for (let i = 0; i < mutation.addedNodes.length; i++) {
                        const node = mutation.addedNodes[i];
                        
                        // Node bir element mi kontrol et
                        if (node.nodeType === 1) {
                            // Sidebar veya içerik eklendiğinde scroll yap
                            if (node.classList && 
                                (node.classList.contains('tutor-course-single-sidebar-wrapper') || 
                                 node.querySelector('.tutor-course-single-sidebar-wrapper'))) {
                                scrollToActiveItem();
                                
                                // İşlem tamamlandı, observer'ı durdur
                                observer.disconnect();
                                break;
                            }
                        }
                    }
                }
            });
        });

        // Tüm DOM değişikliklerini izle
        observer.observe(document.documentElement, {
            childList: true,
            subtree: true
        });
    };

    // Sayfa tamamen yüklendiğinde çalışacak kod
    const onDOMContentLoaded = function() {
        // Scroll işlemini tekrar gerçekleştir (kesin olması için)
        scrollToActiveItem();
        
        // Sayfa yüklendikten 100ms sonra tekrar scroll yap
        // Bu, tüm içeriğin yüklenmesini beklemek için
        setTimeout(scrollToActiveItem, 100);
    };

    // Sayfa tamamen yüklendiğinde ve tüm kaynaklar yüklendiğinde çalışacak kod
    const onWindowLoad = function() {
        // Son bir kez daha scroll yap (tüm resimler ve diğer kaynaklar yüklendikten sonra)
        scrollToActiveItem();
    };

    // Sayfa yüklenmeden önce çalışacak kodu hemen çalıştır
    earlyScrollSetup();
    
    // Sayfa yüklendiğinde çalışacak kodları event listener'lara ekle
    document.addEventListener('DOMContentLoaded', onDOMContentLoaded);
    window.addEventListener('load', onWindowLoad);
})();
