/**
 * Student Course Assignment Styles
 */

/* Modal Overlay */
.tutor-course-assignment-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99998;
    display: none;
}

/* Modal */
.tutor-course-assignment-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    width: 800px;
    max-width: 90%;
    max-height: 85vh;
    z-index: 99999;
    display: none;
    overflow: hidden;
}

/* Modal Header */
.tutor-course-assignment-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e5e5e5;
}

.tutor-course-assignment-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.tutor-course-assignment-modal-close {
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-decoration: none;
    opacity: 0.5;
}

.tutor-course-assignment-modal-close:hover {
    opacity: 1;
    text-decoration: none;
}

/* Modal Body */
.tutor-course-assignment-modal-body {
    padding: 20px;
    max-height: calc(80vh - 70px);
    overflow-y: auto;
}

/* Search Box */
.tutor-course-assignment-search {
    margin-bottom: 10px;
}

.tutor-course-assignment-search input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Course List */
.tutor-course-assignment-list {
    max-height: 500px;
    overflow-y: auto;
}

.tutor-course-assignment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    transition: background-color 0.2s ease;
}

.tutor-course-assignment-item:hover {
    background-color: #f8f9fa;
}

.tutor-course-assignment-item:last-child {
    border-bottom: none;
}

.tutor-course-assignment-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.tutor-course-assignment-thumbnail {
    width: 80px;
    height: 60px;
    margin-right: 15px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
}

.tutor-course-assignment-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tutor-course-assignment-title {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
}

.tutor-course-assignment-actions {
    margin-left: 15px;
}

.tutor-course-assignment-enrolled {
    background-color: #f0f9ff;
}

/* Loading */
.tutor-course-assignment-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* Error */
.tutor-course-assignment-error {
    text-align: center;
    padding: 20px;
    color: #e53935;
}

/* Empty */
.tutor-course-assignment-empty {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* Assign Course Button */
.tutor-assign-course-btn {
    margin-right: 5px !important;
}

/* Responsive */
@media (max-width: 767px) {
    .tutor-course-assignment-modal {
        width: 95%;
    }

    .tutor-course-assignment-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .tutor-course-assignment-actions {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }

    .tutor-course-assignment-actions button {
        width: 100%;
    }
}
