<?php
/**
 * Plugin Name: Dmr LMS
 * Plugin URI: https://tutorlms.com
 * Description: <PERSON><PERSON> <PERSON>klenti, Tutor LMS eklentisinin kurs izleme ekranı ve dashboard tasarımını değiştirir. Orjinal Tutor LMS eklentisi ile birlikte kullanılmalıdır.
 * Version: 1.0.4
 * Author: <PERSON><PERSON><PERSON> Developer
 * Author URI: https://example.com
 * License: GPLv2 or later
 * Text Domain: dmr-lms
 * Domain Path: /languages
 * Requires at least: 5.3
 * Tested up to: 6.7
 * Requires PHP: 7.4
 *
 * @package DmrLMS
 */

/**
 * ---------------------------------------------------------------
 * TEMA MODU (DARK MODE) İŞLEVLERİ
 * ---------------------------------------------------------------
 * Te<PERSON> modu (dark mode) i<PERSON><PERSON> gere<PERSON>ler - localStorage tabanlı
 */

/**
 * Tema modu (dark mode) tercihini getiren fonksiyon
 *
 * Bu fonksiyon, kullanıcının tema modu tercihini döndürür.
 * localStorage kullanıldığı için sadece varsayılan değeri döndürür.
 * Gerçek değer JavaScript tarafında localStorage'dan okunur.
 *
 * @param int $user_id Kullanıcı ID'si (kullanılmıyor, uyumluluk için)
 * @return string 'dark' veya 'light'
 * @since 1.0.4
 */
function dmr_lms_get_theme_mode($user_id = 0) {
    // localStorage kullanıldığı için varsayılan değer döndür
    // Gerçek değer JavaScript tarafında localStorage'dan okunacak
    return 'light';
}

/**
 * ---------------------------------------------------------------
 * GÜVENLİK VE TEMEL TANIMLAMALAR
 * ---------------------------------------------------------------
 * Doğrudan erişimi engelleme ve temel sabitleri tanımlama
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Eklenti sabitleri
define('DMR_LMS_VERSION', '1.0.1');
define('DMR_LMS_FILE', __FILE__);
define('DMR_LMS_PATH', plugin_dir_path(__FILE__));
define('DMR_LMS_URL', plugin_dir_url(__FILE__));
define('DMR_LMS_TEMPLATES_PATH', DMR_LMS_PATH . 'templates/');

/**
 * ---------------------------------------------------------------
 * GEREKLİ DOSYALARI DAHİL ETME
 * ---------------------------------------------------------------
 * Eklentinin çalışması için gerekli olan dosyaları dahil etme
 */

// Ana sınıf tanımını içeren dosyayı yükle
require_once __DIR__ . '/assets/php/class-dmr-lms.php';

// Admin bar ayarları için dosyayı yükle
require_once __DIR__ . '/assets/php/Custom-admin-bar-settings.php';

// Öğrenci kurs atama sınıfını yükle
require_once __DIR__ . '/assets/php/Student_Course_Assignment.php';

// Video tam ekran işleyicisi artık custom-learning-page.js içinde bulunuyor
// require_once __DIR__ . '/includes/video-fullscreen-handler.php'; // Kaldırıldı

/**
 * ---------------------------------------------------------------
 * KURS İZLEME EKRANI KAYNAKLARI
 * ---------------------------------------------------------------
 * Kurs izleme ekranı için gerekli CSS ve JavaScript dosyalarını yükleme
 */

/**
 * Kurs izleme ekranı için stil ve script dosyalarını ekler
 *
 * Bu fonksiyon, modern video oynatıcı ve kurs izleme ekranı için gerekli
 * tüm CSS ve JavaScript dosyalarını WordPress'e kaydeder.
 *
 * @since 1.0.0
 */
function dmr_lms_learning_page_assets() {
    // NOT: Performans optimizasyonu için gereksiz CSS dosyaları kaldırıldı
    // Video oynatıcı ve yükleme animasyonu stilleri custom-learning-page.css içinde birleştirildi

    // Birleştirilmiş kurs izleme ekranı JavaScript dosyası
    wp_enqueue_script(
        'custom-learning-page',
        DMR_LMS_URL . 'assets/js/custom-learning-page.js',
        array('jquery'),
        DMR_LMS_VERSION,
        true
    );

    // Sidebar otomatik scroll JavaScript dosyası - sayfa yüklenmeden önce çalışması için
    wp_enqueue_script(
        'custom-sidebar-scroll',
        DMR_LMS_URL . 'assets/js/Custom-sidebar-scroll.js',
        array(),
        DMR_LMS_VERSION,
        false // false = head bölümünde yükle, sayfa yüklenmeden önce çalışsın
    );
}
add_action('wp_enqueue_scripts', 'dmr_lms_learning_page_assets');

/**
 * ---------------------------------------------------------------
 * GENEL CSS VE DASHBOARD KAYNAKLARI
 * ---------------------------------------------------------------
 * Tüm sayfalar ve dashboard için gerekli CSS ve JavaScript dosyalarını yükleme
 */

/**
 * Tutor LMS için özel CSS ve JavaScript dosyalarını ekler
 *
 * Bu fonksiyon, kurs izleme ekranı ve dashboard için gerekli tüm CSS dosyalarını
 * ve dashboard için JavaScript dosyasını WordPress'e kaydeder.
 * Yüksek öncelik (100) ile çalışır, böylece diğer stil dosyalarını geçersiz kılabilir.
 *
 * @since 1.0.0
 */
function dmr_lms_custom_styles() {
    // Kurs izleme ekranı için özel CSS
    wp_enqueue_style(
        'tutor-custom-styles',
        DMR_LMS_URL . 'assets/css/custom-learning-page.css',
        array(),
        DMR_LMS_VERSION // Cache busting için sadece versiyon kullan
    );

    // Dashboard için özel CSS - Tüm dashboard CSS dosyaları birleştirildi ve optimize edildi
    // NOT: Performans optimizasyonu için gereksiz CSS dosyaları kaldırıldı
    // Yorum sistemi ve modern dashboard stilleri custom-dashboard-page.css içinde birleştirildi
    wp_enqueue_style(
        'tutor-dashboard-custom',
        DMR_LMS_URL . 'assets/css/custom-dashboard-page.css',
        array(),
        DMR_LMS_VERSION
    );

    // Mobil dashboard footer için özel CSS
    wp_enqueue_style(
        'tutor-mobile-footer-custom',
        DMR_LMS_URL . 'assets/css/Custom-mobile-footer.css',
        array(),
        DMR_LMS_VERSION
    );

    // Dark mod butonu için özel CSS
    wp_enqueue_style(
        'tutor-theme-mode-button',
        DMR_LMS_URL . 'assets/css/Custom-theme-mode-button.css',
        array(),
        DMR_LMS_VERSION
    );

    // Dark mode override CSS - tüm dark mode stillerini geçersiz kılmak için
    wp_enqueue_style(
        'tutor-dark-mode-override',
        DMR_LMS_URL . 'assets/css/dark-mode-override.css',
        array(),
        DMR_LMS_VERSION . '.' . time() // Önbelleği engellemek için zaman damgası ekle
    );

    // Mobil cihazlar için özel düzeltmeler CSS
    wp_enqueue_style(
        'tutor-mobile-fixes',
        DMR_LMS_URL . 'assets/css/custom-mobile-fixes.css',
        array(),
        DMR_LMS_VERSION
    );

    // Quiz sayfası için özel CSS
    wp_enqueue_style(
        'tutor-quiz-styles',
        DMR_LMS_URL . 'assets/css/Custom-quiz-styles.css',
        array(),
        DMR_LMS_VERSION
    );

    // Accordion düzeltmeleri için özel CSS
    wp_enqueue_style(
        'tutor-accordion-fixes',
        DMR_LMS_URL . 'assets/css/Custom-accordion-fixes.css',
        array(),
        DMR_LMS_VERSION
    );

    // NOT: Performans iyileştirmeleri CSS dosyası kaldırıldı - gereksiz dosya

    // Dashboard için birleştirilmiş JavaScript dosyası
    wp_enqueue_script(
        'custom-dashboard-page',
        DMR_LMS_URL . 'assets/js/custom-dashboard-page.js',
        array('jquery'),
        DMR_LMS_VERSION,
        true
    );

    // Özel rating yazısını düzenleyen JavaScript dosyası
    wp_enqueue_script(
        'custom-rating',
        DMR_LMS_URL . 'assets/js/custom-rating.js',
        array('jquery'),
        DMR_LMS_VERSION,
        true
    );


}
add_action('wp_enqueue_scripts', 'dmr_lms_custom_styles', 100); // Yüksek öncelik (100) eklendi

/**
 * ---------------------------------------------------------------
 * DASHBOARD VE RENK DEĞİŞKENLERİ
 * ---------------------------------------------------------------
 * Dashboard Spotlight modu ve renk değişkenleri için gerekli kaynakları yükleme
 */

/**
 * Dashboard Spotlight modu için JavaScript verilerini ekler - Optimize edilmiş
 *
 * Bu fonksiyon, dashboard spotlight modu için gerekli verileri JavaScript'e gönderir.
 * Logo URL'si, site adı ve spotlight modu durumu gibi bilgileri içerir.
 * Performans için optimize edilmiştir.
 *
 * @since 1.0.1
 */
function dmr_lms_dashboard_spotlight_data() {
    // Sadece dashboard sayfalarında yükle
    if (!is_admin() && function_exists('tutor_utils') &&
        (is_page('dashboard') || is_page('student-dashboard') ||
         (isset($_GET['tutor_dashboard_page']) || isset($_GET['course_id'])))) {

        // JavaScript'e gerekli verileri sağla - statik değişkenlerle önbelleğe al
        static $spotlight_data = null;

        if (null === $spotlight_data) {
            $logo_url = DMR_LMS_URL . 'assets/images/logo 2.png';
            $site_name = get_bloginfo('name');

            // Spotlight modu aktif mi kontrol et - transient cache kullan
            $cache_key = 'tutor_spotlight_mode_status';
            $enable_spotlight_mode = get_transient($cache_key);

            if (false === $enable_spotlight_mode) {
                $enable_spotlight_mode = tutor_utils()->get_option('enable_spotlight_mode');
                // 1 saat süreyle cache'le
                set_transient($cache_key, $enable_spotlight_mode, HOUR_IN_SECONDS);
            }

            $spotlight_data = array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'logo_url' => $logo_url,
                'site_name' => $site_name,
                'spotlight_enabled' => $enable_spotlight_mode === 'on' ? true : false,
                'plugin_url' => DMR_LMS_URL
            );
        }

        // JavaScript'e veri gönder
        wp_localize_script(
            'custom-dashboard-page',
            'tutor_dashboard_spotlight_data',
            $spotlight_data
        );
    }
}
add_action('wp_enqueue_scripts', 'dmr_lms_dashboard_spotlight_data');

/**
 * Renk değişkenlerini hesaplayan JavaScript'i ekler
 *
 * Bu fonksiyon, Tutor LMS'in renk değişkenlerini hesaplayan ve CSS değişkenleri olarak
 * ekleyen JavaScript dosyasını WordPress'e kaydeder. Düşük öncelik (5) ile erken yüklenir.
 *
 * @since 1.0.0
 */
function dmr_lms_color_variables() {
    // Tüm sayfalarda yükle
    wp_enqueue_script(
        'tutor-color-variables',
        DMR_LMS_URL . 'assets/js/color-variables.js',
        array('jquery'),
        DMR_LMS_VERSION,
        true
    );
}
add_action('wp_enqueue_scripts', 'dmr_lms_color_variables', 5); // Düşük öncelik (5) ile erken yükle

/**
 * ---------------------------------------------------------------
 * KURS İZLEME DENEYİMİ OPTİMİZASYONLARI
 * ---------------------------------------------------------------
 * Kurs izleme deneyimini iyileştiren ve performansı artıran işlevler
 */

/**
 * Sayfa yüklenmeden önce sidebar scroll işlemini gerçekleştiren script
 * Bu script, sayfa içeriği yüklenmeden önce sidebar'ı aktif içeriğe scroll yapar
 *
 * @since 1.0.2
 */
function dmr_lms_early_sidebar_scroll_script() {
    // Sadece kurs izleme sayfalarında yükle
    if (function_exists('is_singular') && (is_singular('lesson') || is_singular('tutor_quiz') || is_singular(tutor()->course_post_type))) {
        ?>
        <script>
        // Sayfa yüklenmeden önce sidebar scroll işlemini gerçekleştir
        (function() {
            // Sayfa yüklendikten sonra çalışacak fonksiyon
            function scrollToActiveItem() {
                // Aktif içerik elementini bul
                var activeItem = document.querySelector('.tutor-course-topic-item.is-active');

                // Sidebar elementini bul
                var sidebar = document.querySelector('.tutor-course-single-sidebar-wrapper');

                // Eğer aktif içerik ve sidebar varsa
                if (activeItem && sidebar) {
                    // Aktif içeriğin pozisyonunu hesapla
                    var activeItemTop = activeItem.offsetTop;

                    // Sidebar'ın yüksekliğini al
                    var sidebarHeight = sidebar.clientHeight;

                    // Aktif içeriği sidebar'ın ortasına getir
                    var scrollPosition = activeItemTop - (sidebarHeight / 2);

                    // Scroll pozisyonunu ayarla (negatif değer olmamasını sağla)
                    sidebar.scrollTop = Math.max(0, scrollPosition);
                }
            }

            // DOM yüklendiğinde scroll işlemini gerçekleştir
            document.addEventListener('DOMContentLoaded', function() {
                // Scroll işlemini gerçekleştir
                scrollToActiveItem();

                // 100ms sonra tekrar dene (tüm içerik yüklenmiş olabilir)
                setTimeout(scrollToActiveItem, 100);
            });

            // Sayfa tamamen yüklendiğinde tekrar scroll işlemini gerçekleştir
            window.addEventListener('load', scrollToActiveItem);
        })();
        </script>
        <?php
    }
}
add_action('wp_head', 'dmr_lms_early_sidebar_scroll_script', 10); // Öncelik 10 ile çalıştır

/**
 * ---------------------------------------------------------------
 * DERS TAMAMLAMA İŞLEVLERİ
 * ---------------------------------------------------------------
 * Ders tamamlama işlemleri için gerekli AJAX verilerini ve işleyicileri içerir
 */

/**
 * Ders tamamlama için AJAX verilerini ekler - Optimize edilmiş
 *
 * Bu fonksiyon, ders tamamlama işlemleri için gerekli AJAX verilerini
 * JavaScript'e gönderir. Sadece ders sayfalarında çalışır.
 * Performans için optimize edilmiştir.
 *
 * @since 1.0.1
 */
function dmr_lms_lesson_completion_data() {
    // Sadece ders sayfalarında yükle
    if (function_exists('is_singular') && is_singular('lesson')) {
        // Mevcut ders ID'sini al
        $lesson_id = get_the_ID();

        // Kurs ID'sini al ve önbelleğe al
        static $course_id = null;
        if (null === $course_id) {
            $course_id = tutor_utils()->get_course_id_by_lesson($lesson_id);
        }

        // AJAX için gerekli verileri ekle
        wp_localize_script(
            'custom-learning-page',
            'tutor_data',
            array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('tutor_nonce'),
                'course_id' => $course_id,
                'lesson_id' => $lesson_id
            )
        );
    }
}
add_action('wp_enqueue_scripts', 'dmr_lms_lesson_completion_data');

/**
 * Ders tamamlama durumunu değiştiren AJAX işleyicisi - Optimize edilmiş
 *
 * Bu fonksiyon, ders tamamlama butonuna tıklandığında çalışır ve
 * dersin tamamlanma durumunu değiştirir. Tamamlanmış dersi geri alabilir
 * veya tamamlanmamış dersi tamamlayabilir. Performans için optimize edilmiştir.
 *
 * @since 1.0.1
 */
function dmr_lms_toggle_lesson_completion() {
    // Güvenlik: Nonce kontrolü
    check_ajax_referer('tutor_nonce', '_wpnonce');

    // Güvenlik: Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'Lütfen giriş yapın'));
        return;
    }

    // Parametreleri al
    $lesson_id = isset($_POST['lesson_id']) ? intval($_POST['lesson_id']) : 0;
    $is_completed = isset($_POST['is_completed']) ? $_POST['is_completed'] === 'true' : false;
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    $user_id = get_current_user_id();

    // Geçerlilik kontrolü
    if (!$lesson_id) {
        wp_send_json_error(array('message' => 'Geçersiz ders ID'));
        return;
    }

    // Kurs ID'si gönderilmediyse bul
    if (!$course_id) {
        $course_id = tutor_utils()->get_course_id_by_lesson($lesson_id);

        // Geçerlilik kontrolü
        if (!$course_id) {
            wp_send_json_error(array('message' => 'Geçersiz kurs ID'));
            return;
        }
    }

    // Cache anahtarı - ilerleme verilerini temizlemek için
    $cache_key = 'tutor_course_progress_' . $course_id . '_' . $user_id;

    // Ders tamamlama durumunu değiştir
    if ($is_completed) {
        // Durum: Tamamlanmış dersi geri al
        delete_user_meta($user_id, '_tutor_completed_lesson_id_' . $lesson_id);

        // Tutor LMS'in kendi hook'larını çağır
        do_action('tutor_mark_lesson_uncomplete_before', $lesson_id, $user_id);
        do_action('tutor_mark_lesson_uncomplete_after', $lesson_id, $user_id);
    } else {
        // Durum: Dersi tamamla
        do_action('tutor_mark_lesson_complete_before', $lesson_id, $user_id);
        update_user_meta($user_id, '_tutor_completed_lesson_id_' . $lesson_id, tutor_time());
        do_action('tutor_mark_lesson_complete_after', $lesson_id, $user_id);
    }

    // Cache'i temizle - yeni ilerleme durumu için
    delete_transient($cache_key);

    // Topic bazlı cache'leri de temizle
    $topics = tutor_utils()->get_topics($course_id);
    if ($topics && $topics->have_posts()) {
        while ($topics->have_posts()) {
            $topics->the_post();
            $topic_id = get_the_ID();
            delete_transient('tutor_topic_progress_' . $topic_id . '_' . $user_id);
        }
        wp_reset_postdata();
    }

    // Kurs ilerleme durumunu al
    $course_progress = tutor_utils()->get_course_completed_percent($course_id);
    $completed_lessons = tutor_utils()->get_completed_lesson_count_by_course($course_id);
    $total_lessons = tutor_utils()->get_lesson_count_by_course($course_id);

    // Yeni ilerleme verilerini cache'le
    $progress_data = array(
        'completed_percent' => $course_progress,
        'completed_lessons' => $completed_lessons,
        'total_lessons' => $total_lessons
    );

    // 5 dakika süreyle cache'le
    set_transient($cache_key, $progress_data, 5 * MINUTE_IN_SECONDS);

    // Başarılı yanıt gönder
    wp_send_json_success(array(
        'completed' => !$is_completed,
        'message' => $is_completed ? 'Ders tamamlanma durumu kaldırıldı' : 'Ders tamamlandı',
        'completed_percent' => $course_progress,
        'completed_lessons' => $completed_lessons,
        'total_lessons' => $total_lessons,
        'course_id' => $course_id,
        'lesson_id' => $lesson_id
    ));
}
add_action('wp_ajax_toggle_lesson_completion', 'dmr_lms_toggle_lesson_completion');

/**
 * Kurs ilerleme durumunu getiren AJAX işleyicisi - Optimize edilmiş
 *
 * Bu fonksiyon, kurs ilerleme durumunu AJAX isteği ile getirir.
 * Tamamlanma yüzdesi, tamamlanan ders sayısı ve toplam ders sayısı
 * gibi bilgileri içerir. Performans için optimize edilmiştir.
 *
 * @since 1.0.1
 */
function dmr_lms_get_course_progress() {
    // Güvenlik: Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        wp_send_json_error('Lütfen giriş yapın');
        return;
    }

    // Gerekli parametreleri al
    $lesson_id = isset($_POST['lesson_id']) ? intval($_POST['lesson_id']) : 0;
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;

    // Ders ID'si varsa ve kurs ID'si yoksa, kurs ID'sini bul
    if ($lesson_id && !$course_id) {
        // Tutor LMS fonksiyonlarını kullanarak kurs ID'sini bul - en hızlı yöntem
        $course_id = tutor_utils()->get_course_id_by_lesson($lesson_id);
    }

    // Kurs bulunamadıysa hata döndür
    if (!$course_id) {
        wp_send_json_error(array('message' => 'Kurs bulunamadı'));
        return;
    }

    // Kurs ilerleme durumunu al - transient cache kullanarak performansı artır
    $user_id = get_current_user_id();
    $cache_key = 'tutor_course_progress_' . $course_id . '_' . $user_id;
    $progress_data = get_transient($cache_key);

    if (false === $progress_data) {
        // Cache'de yoksa hesapla
        $course_progress = tutor_utils()->get_course_completed_percent($course_id);
        $completed_lessons = tutor_utils()->get_completed_lesson_count_by_course($course_id);
        $total_lessons = tutor_utils()->get_lesson_count_by_course($course_id);

        $progress_data = array(
            'completed_percent' => $course_progress,
            'completed_lessons' => $completed_lessons,
            'total_lessons' => $total_lessons
        );

        // 5 dakika süreyle cache'le
        set_transient($cache_key, $progress_data, 5 * MINUTE_IN_SECONDS);
    }

    // Yanıta kurs ve ders ID'lerini ekle
    $progress_data['course_id'] = $course_id;
    $progress_data['lesson_id'] = $lesson_id;

    // Başarılı yanıt gönder
    wp_send_json_success($progress_data);
}
add_action('wp_ajax_get_course_progress', 'dmr_lms_get_course_progress');

/**
 * Topic tamamlanma sayılarını getiren AJAX işleyicisi
 *
 * Bu fonksiyon, bir kurs içindeki tüm topic'lerin tamamlanma sayılarını getirir.
 * Her topic için tamamlanan içerik sayısı ve toplam içerik sayısını döndürür.
 * Ders tamamlama butonuna tıklandığında topic başlıklarındaki ilerleme sayılarını
 * güncellemek için kullanılır.
 *
 * @since 1.0.3
 */
function dmr_lms_get_topic_completion_counts() {
    // Güvenlik: Nonce kontrolü
    check_ajax_referer('tutor_nonce', '_wpnonce');

    // Güvenlik: Kullanıcı giriş yapmış mı kontrol et
    if (!is_user_logged_in()) {
        wp_send_json_error(array('message' => 'Lütfen giriş yapın'));
        return;
    }

    // Parametreleri al
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    $lesson_id = isset($_POST['lesson_id']) ? intval($_POST['lesson_id']) : 0;
    $user_id = get_current_user_id();

    // Geçerlilik kontrolü
    if (!$course_id) {
        wp_send_json_error(array('message' => 'Geçersiz kurs ID'));
        return;
    }

    // Kurs içindeki tüm topic'leri al
    $topics = tutor_utils()->get_topics($course_id);

    if (!$topics || !$topics->have_posts()) {
        wp_send_json_error(array('message' => 'Kurs içinde topic bulunamadı'));
        return;
    }

    // Her topic için tamamlanma sayılarını hesapla
    $topic_counts = array();

    while ($topics->have_posts()) {
        $topics->the_post();
        $topic_id = get_the_ID();

        // Topic içindeki tamamlanma sayılarını al - kullanıcı bazlı hesaplama
        $total_contents = tutor_utils()->count_completed_contents_by_topic($topic_id, $user_id);

        // Eğer kullanıcı bazlı hesaplama başarısız olursa genel hesaplama yap
        if (!$total_contents || !isset($total_contents['contents'])) {
            $total_contents = tutor_utils()->count_completed_contents_by_topic($topic_id);
        }

        // Sonuç dizisine ekle
        $topic_counts[$topic_id] = array(
            'completed' => isset($total_contents['completed']) ? intval($total_contents['completed']) : 0,
            'total' => isset($total_contents['contents']) ? intval($total_contents['contents']) : 0
        );
    }

    // WordPress post verilerini sıfırla
    wp_reset_postdata();

    // Başarılı yanıt gönder
    wp_send_json_success($topic_counts);
}
add_action('wp_ajax_get_topic_completion_counts', 'dmr_lms_get_topic_completion_counts');

/**
 * ---------------------------------------------------------------
 * DASHBOARD MENÜ ÖZELLEŞTİRMELERİ
 * ---------------------------------------------------------------
 * Dashboard sidebar menüsüne özel menü öğeleri ekleme
 */

/**
 * Dashboard menüsüne "Kurs Duyuruları" menü öğesini ekler
 *
 * Bu fonksiyon, Tutor LMS dashboard menüsüne "Kurs Duyuruları" menü öğesini
 * "Soru Cevap" sekmesinin üstüne ekler. Kullanıcının kayıtlı olduğu kurslardaki
 * duyuruları görüntülemek için kullanılır.
 *
 * @since 1.0.6
 * @param array $items Mevcut menü öğeleri
 * @return array Güncellenmiş menü öğeleri
 */
function dmr_lms_add_course_announcements_menu($items) {
    // "question-answer" menüsünden önce "course-announcements" menüsünü ekle
    $new_items = array();

    foreach ($items as $key => $item) {
        // "question-answer" menüsünden önce kurs duyuruları menüsünü ekle
        if ($key === 'question-answer') {
            $new_items['course-announcements'] = array(
                'title' => __('Kurs Duyuruları', 'dmr-lms'),
                'icon'  => 'tutor-icon-bullhorn',
            );
        }

        $new_items[$key] = $item;
    }

    return $new_items;
}
add_filter('tutor_dashboard/nav_items', 'dmr_lms_add_course_announcements_menu', 10);

/**
 * ---------------------------------------------------------------
 * KURS DUYURULARI TEST VE DEBUG İŞLEVLERİ
 * ---------------------------------------------------------------
 * Kurs duyuruları sisteminin test edilmesi için yardımcı fonksiyonlar
 */

/**
 * Kurs duyuruları sisteminin durumunu kontrol eder
 *
 * Bu fonksiyon, kurs duyuruları sisteminin düzgün çalışıp çalışmadığını
 * kontrol eder ve debug bilgileri sağlar.
 *
 * @since 1.0.6
 * @return array Sistem durumu bilgileri
 */
function dmr_lms_check_announcements_system_status() {
    $status = array(
        'menu_added' => false,
        'template_exists' => false,
        'functions_loaded' => false,
        'cache_working' => false,
        'user_enrolled_courses' => 0,
        'total_announcements' => 0,
        'errors' => array()
    );

    try {
        // Menü eklenmiş mi kontrol et
        $dashboard_pages = tutor_utils()->tutor_dashboard_nav_ui_items();
        $status['menu_added'] = isset($dashboard_pages['course-announcements']);

        // Template dosyası var mı kontrol et
        $template_path = DMR_LMS_TEMPLATES_PATH . 'dashboard/course-announcements.php';
        $status['template_exists'] = file_exists($template_path);

        // Fonksiyonlar yüklenmiş mi kontrol et
        $status['functions_loaded'] = function_exists('dmr_lms_get_user_course_announcements');

        // Cache çalışıyor mu kontrol et
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $cache_key = 'dmr_lms_test_cache_' . $user_id;
            set_transient($cache_key, 'test_value', 60);
            $status['cache_working'] = get_transient($cache_key) === 'test_value';
            delete_transient($cache_key);

            // Kullanıcı verilerini al
            $status['user_enrolled_courses'] = dmr_lms_get_user_enrolled_courses_count($user_id);
            $status['total_announcements'] = dmr_lms_get_user_total_announcements_count($user_id);
        }

    } catch (Exception $e) {
        $status['errors'][] = $e->getMessage();
    }

    return $status;
}

/**
 * Debug bilgilerini admin bar'a ekler (sadece admin kullanıcılar için)
 *
 * @since 1.0.6
 */
function dmr_lms_add_debug_info_to_admin_bar() {
    if (!current_user_can('manage_options') || !is_admin_bar_showing()) {
        return;
    }

    global $wp_admin_bar;

    $status = dmr_lms_check_announcements_system_status();
    $all_working = $status['menu_added'] && $status['template_exists'] &&
                   $status['functions_loaded'] && $status['cache_working'];

    $title = $all_working ?
        '✅ Kurs Duyuruları: Çalışıyor' :
        '❌ Kurs Duyuruları: Sorun Var';

    $wp_admin_bar->add_node(array(
        'id'    => 'dmr-lms-announcements-status',
        'title' => $title,
        'href'  => '#',
        'meta'  => array(
            'title' => 'Kurs Duyuruları Sistem Durumu'
        )
    ));
}
add_action('admin_bar_menu', 'dmr_lms_add_debug_info_to_admin_bar', 100);

/**
 * ---------------------------------------------------------------
 * KURS DUYURULARI VERİ İŞLEVLERİ
 * ---------------------------------------------------------------
 * Kullanıcının kayıtlı olduğu kurslardaki duyuruları çekme ve filtreleme işlevleri
 */

/**
 * Kullanıcının kayıtlı olduğu kurslardaki duyuruları getirir
 *
 * Bu fonksiyon, belirtilen kullanıcının kayıtlı olduğu tüm kurslardaki
 * duyuruları çeker ve belirtilen kriterlere göre filtreler.
 *
 * @since 1.0.6
 * @param int $user_id Kullanıcı ID'si (0 ise mevcut kullanıcı)
 * @param array $args Sorgu parametreleri
 * @return WP_Query Duyuru sorgu sonucu
 */
function dmr_lms_get_user_course_announcements($user_id = 0, $args = array()) {
    // Kullanıcı ID'sini al
    $user_id = $user_id ? $user_id : get_current_user_id();

    if (!$user_id) {
        return new WP_Query(); // Boş sorgu döndür
    }

    // Kullanıcının kayıtlı olduğu kursları al - cache ile optimize et
    $cache_key = 'dmr_lms_enrolled_courses_' . $user_id;
    $enrolled_course_ids = get_transient($cache_key);

    if (false === $enrolled_course_ids) {
        $enrolled_course_ids = tutor_utils()->get_enrolled_courses_ids_by_user($user_id);
        // 30 dakika cache
        set_transient($cache_key, $enrolled_course_ids, 30 * MINUTE_IN_SECONDS);
    }

    if (empty($enrolled_course_ids)) {
        return new WP_Query(); // Kayıtlı kurs yoksa boş sorgu döndür
    }

    // Varsayılan sorgu parametreleri
    $default_args = array(
        'post_type'       => 'tutor_announcements',
        'post_status'     => 'publish',
        'post_parent__in' => $enrolled_course_ids,
        'posts_per_page'  => 10,
        'orderby'         => 'date',
        'order'           => 'DESC',
        'meta_query'      => array(
            'relation' => 'AND',
        ),
    );

    // Parametreleri birleştir
    $query_args = wp_parse_args($args, $default_args);

    // Sorguyu çalıştır
    return new WP_Query($query_args);
}

/**
 * Kullanıcının belirli bir kurstaki duyurularını getirir
 *
 * Bu fonksiyon, kullanıcının belirli bir kursa kayıtlı olup olmadığını kontrol eder
 * ve kayıtlıysa o kurstaki duyuruları getirir.
 *
 * @since 1.0.6
 * @param int $course_id Kurs ID'si
 * @param int $user_id Kullanıcı ID'si (0 ise mevcut kullanıcı)
 * @param array $args Ek sorgu parametreleri
 * @return WP_Query|false Duyuru sorgu sonucu veya false
 */
function dmr_lms_get_course_announcements_for_user($course_id, $user_id = 0, $args = array()) {
    $user_id = $user_id ? $user_id : get_current_user_id();

    if (!$user_id || !$course_id) {
        return false;
    }

    // Kullanıcının bu kursa kayıtlı olup olmadığını kontrol et
    if (!tutor_utils()->is_enrolled($course_id, $user_id)) {
        return false;
    }

    // Varsayılan parametreler
    $default_args = array(
        'post_parent' => $course_id,
        'posts_per_page' => -1,
    );

    $query_args = wp_parse_args($args, $default_args);

    return dmr_lms_get_user_course_announcements($user_id, $query_args);
}

/**
 * Kullanıcının kayıtlı olduğu kursların sayısını getirir
 *
 * @since 1.0.6
 * @param int $user_id Kullanıcı ID'si
 * @return int Kayıtlı olunan kurs sayısı
 */
function dmr_lms_get_user_enrolled_courses_count($user_id = 0) {
    $user_id = $user_id ? $user_id : get_current_user_id();

    if (!$user_id) {
        return 0;
    }

    $enrolled_course_ids = tutor_utils()->get_enrolled_courses_ids_by_user($user_id);
    return is_array($enrolled_course_ids) ? count($enrolled_course_ids) : 0;
}

/**
 * Kullanıcının kayıtlı olduğu kurslardaki toplam duyuru sayısını getirir
 *
 * @since 1.0.6
 * @param int $user_id Kullanıcı ID'si
 * @return int Toplam duyuru sayısı
 */
function dmr_lms_get_user_total_announcements_count($user_id = 0) {
    $user_id = $user_id ? $user_id : get_current_user_id();

    if (!$user_id) {
        return 0;
    }

    // Cache anahtarı
    $cache_key = 'dmr_lms_total_announcements_' . $user_id;
    $count = get_transient($cache_key);

    if (false === $count) {
        $query = dmr_lms_get_user_course_announcements($user_id, array(
            'posts_per_page' => -1,
            'fields' => 'ids', // Sadece ID'leri al, performans için
        ));

        $count = $query->found_posts;

        // 15 dakika cache
        set_transient($cache_key, $count, 15 * MINUTE_IN_SECONDS);
    }

    return (int) $count;
}

/**
 * Kurs duyuruları cache'ini temizler
 *
 * Bu fonksiyon, kullanıcının kurs duyuruları ile ilgili cache'lerini temizler.
 * Yeni duyuru eklendiğinde veya kullanıcı yeni kursa kaydolduğunda çağrılır.
 *
 * @since 1.0.6
 * @param int $user_id Kullanıcı ID'si (0 ise tüm kullanıcılar)
 */
function dmr_lms_clear_announcements_cache($user_id = 0) {
    if ($user_id) {
        // Belirli kullanıcının cache'ini temizle
        delete_transient('dmr_lms_enrolled_courses_' . $user_id);
        delete_transient('dmr_lms_total_announcements_' . $user_id);
    } else {
        // Tüm kullanıcıların cache'ini temizle (performans için dikkatli kullan)
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_dmr_lms_enrolled_courses_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_dmr_lms_total_announcements_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_dmr_lms_enrolled_courses_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_dmr_lms_total_announcements_%'");
    }
}

/**
 * Yeni duyuru eklendiğinde cache'i temizle
 */
add_action('tutor_announcements/after/save', function($post_id, $announcement, $action_type) {
    // Tüm kullanıcıların duyuru cache'ini temizle
    dmr_lms_clear_announcements_cache();
}, 10, 3);

/**
 * Kullanıcı kursa kaydolduğunda cache'i temizle
 */
add_action('tutor_after_enrolled', function($course_id, $user_id) {
    dmr_lms_clear_announcements_cache($user_id);
}, 10, 2);

/**
 * ---------------------------------------------------------------
 * TEMA MODU (DARK MODE) İŞLEVLERİ
 * ---------------------------------------------------------------
 * Tema modu (dark mode) için gerekli işlevler
 */

/**
 * Dashboard header'a dark mod butonunu ekler
 *
 * Bu fonksiyon, dashboard header'a dark mod butonunu ekler.
 * Buton, gündüz modunda güneş ikonu, gece modunda hilal ikonu gösterir.
 *
 * @since 1.0.5
 */
function dmr_lms_add_theme_mode_button() {
    // Tema modu butonunu ekle
    include_once DMR_LMS_TEMPLATES_PATH . 'Custom-theme-mode-button.php';
}
add_action('tutor_dashboard/before_header_button', 'dmr_lms_add_theme_mode_button');

/**
 * Tema modu (dark mode) tercihini getiren fonksiyon
 *
 * Bu fonksiyon, kullanıcının tema modu tercihini döndürür.
 * Önce kullanıcı meta verisine, sonra oturum verisine bakar.
 *
 * @return string 'dark' veya 'light'
 * @since 1.0.3
 */
// Bu fonksiyon artık kullanılmıyor, yerine dmr_lms_get_theme_mode($user_id = 0) fonksiyonu kullanılıyor
// Fonksiyon adını değiştirerek çakışmayı önlüyoruz
function dmr_lms_get_theme_mode_legacy() {
    // Yeni fonksiyonu çağır
    return dmr_lms_get_theme_mode();
}

/**
 * ---------------------------------------------------------------
 * PERFORMANS OPTİMİZASYONLARI
 * ---------------------------------------------------------------
 * Sayfa yükleme hızını artıran performans optimizasyonları
 */

/**
 * CSS dosyalarını optimize eder
 *
 * Bu fonksiyon, CSS dosyalarını optimize ederek sayfa yükleme hızını artırır.
 * CSS dosyalarını birleştirir, gereksiz boşlukları kaldırır ve önbelleğe alır.
 *
 * @since 1.0.2
 */
function dmr_lms_optimize_css() {
    // CSS dosyalarını optimize etmek için filtre ekle
    add_filter('style_loader_tag', 'dmr_lms_optimize_css_tag', 10, 4);

    // CSS dosyalarını önbelleğe almak için filtre ekle
    add_filter('style_loader_src', 'dmr_lms_cache_css', 10, 2);

    // JavaScript dosyalarını optimize etmek için filtre ekle
    add_filter('script_loader_tag', 'dmr_lms_optimize_js_tag', 10, 3);
}
add_action('init', 'dmr_lms_optimize_css');

/**
 * CSS etiketlerini optimize eder
 *
 * Bu fonksiyon, CSS etiketlerini optimize ederek sayfa yükleme hızını artırır.
 * Gereksiz öznitelikleri kaldırır ve async/defer özniteliklerini ekler.
 *
 * @since 1.0.2
 * @param string $tag    CSS etiketi
 * @param string $handle CSS tanımlayıcısı
 * @param string $src    CSS dosyasının URL'si
 * @param string $media  CSS medya özniteliği
 * @return string        Optimize edilmiş CSS etiketi
 */
function dmr_lms_optimize_css_tag($tag, $handle, $src, $media) {
    // Sadece DMR LMS CSS dosyalarını optimize et
    if (strpos($handle, 'tutor-') === 0) {
        // Gereksiz type özniteliğini kaldır
        $tag = str_replace(" type='text/css'", '', $tag);

        // Medya özniteliğini optimize et
        if ($media === 'all') {
            $tag = str_replace(" media='all'", '', $tag);
        }

        // Preload özniteliğini ekle - kritik CSS dosyaları için
        if (in_array($handle, array('tutor-dashboard-custom', 'tutor-custom-styles'))) {
            $tag = str_replace('<link', '<link rel="preload" as="style" onload="this.onload=null;this.rel=\'stylesheet\'"', $tag);
            $tag .= "<noscript><link rel='stylesheet' href='$src' media='$media'></noscript>";
        }
    }

    return $tag;
}

/**
 * CSS dosyalarını önbelleğe alır
 *
 * Bu fonksiyon, CSS dosyalarını önbelleğe alarak sayfa yükleme hızını artırır.
 * Dosya sürümünü değiştirerek tarayıcı önbelleğini kontrol eder.
 *
 * @since 1.0.2
 * @param string $src    CSS dosyasının URL'si
 * @param string $handle CSS tanımlayıcısı
 * @return string        Önbelleğe alınmış CSS dosyasının URL'si
 */
function dmr_lms_cache_css($src, $handle) {
    // Sadece DMR LMS CSS dosyalarını önbelleğe al
    if (strpos($handle, 'tutor-') === 0 && !empty($src)) {
        // Önbellek süresi: 1 hafta (saniye cinsinden)
        $cache_time = 604800;

        // Önbellek parametresi ekle
        $src = add_query_arg('ver', DMR_LMS_VERSION . '.' . floor(time() / $cache_time), $src);
    }

    return $src;
}

/**
 * JavaScript etiketlerini optimize eder
 *
 * Bu fonksiyon, JavaScript etiketlerini optimize ederek sayfa yükleme hızını artırır.
 * Gereksiz öznitelikleri kaldırır ve async/defer özniteliklerini ekler.
 *
 * @since 1.0.2
 * @param string $tag    JavaScript etiketi
 * @param string $handle JavaScript tanımlayıcısı
 * @param string $src    JavaScript dosyasının URL'si
 * @return string        Optimize edilmiş JavaScript etiketi
 */
function dmr_lms_optimize_js_tag($tag, $handle, $src) {
    // Sadece DMR LMS JavaScript dosyalarını optimize et
    if (strpos($handle, 'custom-') === 0 || strpos($handle, 'tutor-') === 0) {
        // Gereksiz type özniteliğini kaldır
        $tag = str_replace(" type='text/javascript'", '', $tag);

        // Defer özniteliğini ekle - sayfa yükleme hızını artırır
        if (!is_admin()) {
            // Kritik olmayan JavaScript dosyaları için defer ekle
            // custom-sidebar-scroll dosyasını hariç tut - sayfa yüklenmeden önce çalışması gerekiyor
            if (!in_array($handle, array('jquery', 'tutor-color-variables', 'custom-sidebar-scroll'))) {
                $tag = str_replace('<script', '<script defer', $tag);
            }
        }
    }

    return $tag;
}

/**
 * ---------------------------------------------------------------
 * ADMIN PANELİ DÜZENLEMELERİ
 * ---------------------------------------------------------------
 * Admin panelindeki Tutor LMS menüsünü düzenleyen fonksiyonlar
 */

/**
 * Admin panelindeki "Pro'ya yükselt" menü öğesini gizler ve admin bar ayarlarını düzenler
 *
 * Bu fonksiyon, WordPress admin panelindeki Tutor LMS menüsünde bulunan
 * "Pro'ya yükselt" menü öğesini gizlemek için özel CSS dosyasını yükler.
 * Ayrıca kullanıcı profil sayfasındaki "Araç çubuğu" yazısını ve seçeneğini gizler.
 *
 * @since 1.0.1
 */
function dmr_lms_hide_pro_menu() {
    // Admin CSS dosyasını yükle - hem admin panelinde hem de ön yüzde
    wp_enqueue_style(
        'tutor-admin-fixes',
        DMR_LMS_URL . 'assets/css/Custom-admin-fixes.css',
        array(),
        DMR_LMS_VERSION
    );
}
add_action('admin_enqueue_scripts', 'dmr_lms_hide_pro_menu');
add_action('wp_enqueue_scripts', 'dmr_lms_hide_pro_menu');

/**
 * ---------------------------------------------------------------
 * EKLENTİ BAŞLATMA
 * ---------------------------------------------------------------
 * Eklentiyi başlatan ve çalıştıran fonksiyonlar
 */

/**
 * Eklentiyi başlat
 *
 * Bu fonksiyon, Dmr_LMS sınıfının tek örneğini oluşturur ve döndürür.
 * Singleton tasarım desenini kullanarak eklentinin tek bir örneğinin
 * oluşturulmasını sağlar.
 *
 * @since 1.0.0
 * @return Dmr_LMS Eklenti sınıfının tek örneği
 */
function dmr_lms() {
    return Dmr_LMS::instance();
}

// Eklentiyi çalıştır
dmr_lms();
